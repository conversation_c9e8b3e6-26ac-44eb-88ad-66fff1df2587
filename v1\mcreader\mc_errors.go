package mcreader

import "fmt"

// PLCError 表示 PLC 返回的非零 Status/EndCode。
// Code 按三菱文档：A 系列 1byte(Code)；3E Frame 2byte(EndCode)。

type PLCError struct {
    Code uint16
}

func (e PLCError) Error() string {
    if msg, ok := ErrMap[e.Code]; ok {
        return fmt.Sprintf("PLC error 0x%04X: %s", e.Code, msg)
    }
    return fmt.Sprintf("PLC error 0x%04X", e.Code)
}

// ErrMap 常见错误码映射（摘自 QCPU 编程手册）。后续可按需补充。
var ErrMap = map[uint16]string{
    0xC050: "未定义的设备号",
    0xC051: "设备号超范围",
    0xC052: "写入时保护错误",
    0xC056: "格式错误",
} 