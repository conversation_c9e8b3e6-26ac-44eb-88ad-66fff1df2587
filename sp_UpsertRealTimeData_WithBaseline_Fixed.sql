-- 创建支持基准值和补偿值的本机编译存储过程（修复版本）
-- 支持独立更新input和output，使用-999作为"不更新"标志
-- 使用-1作为有效值（如午夜重置时的last_plc_input = -1）

CREATE PROCEDURE [dbo].[sp_UpsertRealTimeData_WithBaseline]
    -- 定义输入参数，数据类型应与表列精确匹配
    @att_date DATE,
    @line_code VARCHAR(50),
    @task_num VARCHAR(50),
    @reg_input INT,                    -- -999表示不更新，其他值（包括-1,0）都是有效值
    @reg_input_time INT,               -- -999表示不更新
    @reg_output INT,                   -- -999表示不更新
    @reg_output_time INT,              -- -999表示不更新
    @updated_datetime datetime,
    @last_plc_input INT,               -- -999表示不更新，-1是有效值
    @last_plc_output INT,              -- -999表示不更新，-1是有效值
    @daily_baseline_input INT,         -- -999表示不更新，-1是有效值
    @daily_baseline_output INT,        -- -999表示不更新，-1是有效值
    @reset_compensation_input INT,     -- -999表示不更新，0和其他值都是有效值
    @reset_compensation_output INT     -- -999表示不更新，0和其他值都是有效值
WITH NATIVE_COMPILATION, SCHEMABINDING
AS
BEGIN ATOMIC WITH (
    TRANSACTION ISOLATION LEVEL = SNAPSHOT,
    LANGUAGE = N'us_english'
)

    DECLARE @FLAG_PUTQTY VARCHAR(10)
    DECLARE @OUTPUT INT
    DECLARE @OUTPUT_TIME INT

    DECLARE @INPUT INT
    DECLARE @INPUT_TIME INT

    DECLARE @LAST_INPUT INT
    DECLARE @LAST_OUTPUT INT

    -- 基准值和补偿值变量
    DECLARE @BASELINE_INPUT INT
    DECLARE @BASELINE_OUTPUT INT
    DECLARE @COMPENSATION_INPUT INT
    DECLARE @COMPENSATION_OUTPUT INT

    -- 处理input相关字段（-999表示不更新）
    IF ISNULL(@reg_input,-999) != -999
    BEGIN
        SET @INPUT = @reg_input
    END

    IF ISNULL(@reg_input_time,-999) != -999
    BEGIN
        SET @INPUT_TIME = @reg_input_time
    END

    -- 处理output相关字段（-999表示不更新）
    IF ISNULL(@reg_output,-999) != -999
    BEGIN
        SET @OUTPUT = @reg_output
    END

    IF ISNULL(@reg_output_time,-999) != -999
    BEGIN
        SET @OUTPUT_TIME = @reg_output_time
    END

    -- 特殊逻辑：某些产线的output使用input值
    IF @line_code IN ('2001')
    BEGIN
        SET @FLAG_PUTQTY = 'Y'
    END

    IF ISNULL(@FLAG_PUTQTY,'N') = 'Y' AND @INPUT IS NOT NULL
    BEGIN
       SET @OUTPUT = @INPUT
       SET @OUTPUT_TIME = @INPUT_TIME
    END

    -- 处理PLC状态字段（-999表示不更新，-1是有效值）
    IF ISNULL(@last_plc_input,-999) != -999
    BEGIN
        SET @LAST_INPUT = @last_plc_input
    END

    IF ISNULL(@last_plc_output,-999) != -999
    BEGIN
        SET @LAST_OUTPUT = @last_plc_output
    END

    -- 处理基准值字段（-999表示不更新，-1是有效值）
    IF ISNULL(@daily_baseline_input,-999) != -999
    BEGIN
        SET @BASELINE_INPUT = @daily_baseline_input
    END

    IF ISNULL(@daily_baseline_output,-999) != -999
    BEGIN
        SET @BASELINE_OUTPUT = @daily_baseline_output
    END

    -- 处理补偿值字段（-999表示不更新，0和其他值都是有效值）
    IF ISNULL(@reset_compensation_input,-999) != -999
    BEGIN
        SET @COMPENSATION_INPUT = @reset_compensation_input
    END

    IF ISNULL(@reset_compensation_output,-999) != -999
    BEGIN
        SET @COMPENSATION_OUTPUT = @reset_compensation_output
    END

    -- 1. 尝试更新现有行
    UPDATE dbo.U_MesLinesRealTimeData
    SET
        updated_datetime = @updated_datetime,
        reg_input = ISNULL(@INPUT, reg_input),                                    -- 只有@INPUT不为NULL时才更新
        reg_input_time = ISNULL(@INPUT_TIME, reg_input_time),                     -- 只有@INPUT_TIME不为NULL时才更新
        reg_output = ISNULL(@OUTPUT, reg_output),                                 -- 只有@OUTPUT不为NULL时才更新
        reg_output_time = ISNULL(@OUTPUT_TIME, reg_output_time),                  -- 只有@OUTPUT_TIME不为NULL时才更新
        last_plc_input = ISNULL(@LAST_INPUT, last_plc_input),                     -- 支持设置为-1
        last_plc_output = ISNULL(@LAST_OUTPUT, last_plc_output),                  -- 支持设置为-1
        daily_baseline_input = ISNULL(@BASELINE_INPUT, daily_baseline_input),     -- 支持设置为-1
        daily_baseline_output = ISNULL(@BASELINE_OUTPUT, daily_baseline_output),  -- 支持设置为-1
        reset_compensation_input = ISNULL(@COMPENSATION_INPUT, reset_compensation_input),   -- 支持设置为0
        reset_compensation_output = ISNULL(@COMPENSATION_OUTPUT, reset_compensation_output) -- 支持设置为0
    WHERE att_date = @att_date AND line_code = @line_code;

    -- 2. 如果没有行被更新，则插入新行
    IF @@ROWCOUNT = 0
    BEGIN
        INSERT INTO dbo.U_MesLinesRealTimeData (
            att_date,
            line_code,
            task_num,
            reg_input,
            reg_input_time,
            reg_output,
            reg_output_time,
            last_plc_input,
            last_plc_output,
            daily_baseline_input,
            daily_baseline_output,
            reset_compensation_input,
            reset_compensation_output
        )
        VALUES (
            @att_date,
            @line_code,
            @task_num,
            ISNULL(@INPUT, 0),                    -- 默认值0
            ISNULL(@INPUT_TIME, 0),               -- 默认值0
            ISNULL(@OUTPUT, 0),                   -- 默认值0
            ISNULL(@OUTPUT_TIME, 0),              -- 默认值0
            ISNULL(@LAST_INPUT, -1),              -- 默认值-1
            ISNULL(@LAST_OUTPUT, -1),             -- 默认值-1
            ISNULL(@BASELINE_INPUT, -1),          -- 默认值-1
            ISNULL(@BASELINE_OUTPUT, -1),         -- 默认值-1
            ISNULL(@COMPENSATION_INPUT, 0),       -- 默认值0
            ISNULL(@COMPENSATION_OUTPUT, 0)       -- 默认值0
        );
    END

    -- 3. 更新小时数据表（只有相应字段被更新时才处理）
    IF @INPUT IS NOT NULL OR @OUTPUT IS NOT NULL
    BEGIN
        UPDATE dbo.U_MesLinesRealTimeDataLine
        SET
            updated_datetime = @updated_datetime,
            hour_input = CASE WHEN @INPUT IS NOT NULL THEN @INPUT ELSE hour_input END,
            hour_output = CASE WHEN @OUTPUT IS NOT NULL THEN @OUTPUT ELSE hour_output END
        WHERE att_date = @att_date AND line_code = @line_code AND hour_qty = datepart(hour,@updated_datetime);

        IF @@ROWCOUNT = 0
        BEGIN
            INSERT INTO dbo.U_MesLinesRealTimeDataLine (
                att_date,
                line_code,
                hour_qty,
                hour_input,
                hour_output
            )
            VALUES (
                @att_date,
                @line_code,
                datepart(hour,@updated_datetime),
                ISNULL(@INPUT, 0),
                ISNULL(@OUTPUT, 0)
            );
        END
    END

    -- 4. 调用任务更新存储过程（需要确保这些是本机编译的存储过程）
    -- 注意：如果sp_UpdateRealTimeDataTask不是本机编译的，需要移除这部分或改为本机编译版本
    /*
    IF @INPUT IS NOT NULL
    BEGIN
        EXEC [dbo].[sp_UpdateRealTimeDataTask] @att_date, @line_code, @INPUT, @INPUT_TIME, @updated_datetime, 1;
    END

    IF @OUTPUT IS NOT NULL
    BEGIN
        EXEC [dbo].[sp_UpdateRealTimeDataTask] @att_date, @line_code, @OUTPUT, @OUTPUT_TIME, @updated_datetime, 0;
    END
    */

END
