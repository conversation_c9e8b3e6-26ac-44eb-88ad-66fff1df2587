package config

import (
	"encoding/json"
	"fmt" // 新增导入，因为 ConfigurationError 的 Error() 方法会使用它
	"log" // Added for logging warnings
	"os"
	"strconv"
)

// ConfigurationError 是一个自定义错误类型，用于表示配置加载或校验相关的错误。
type ConfigurationError struct {
	Msg string // 错误信息
}

// Error 实现 error 接口。
func (e *ConfigurationError) Error() string {
	return fmt.Sprintf("Configuration Error: %s", e.Msg)
}

// AppConfig 是整个应用程序的根配置结构体
type AppConfig struct {
	Database DatabaseConfig `json:"database"`
	// Lines           []LineControllerConfig `json:"lines"` // Removed as per new design
	PollingRuntimes []PollingRuntimeConfig `json:"pollingRuntimes"`
	Scheduler       SchedulerConfig        `json:"scheduler"`
	Logging         LoggingConfig          `json:"logging"`
}

// DatabaseConfig 定义了数据库连接参数
type DatabaseConfig struct {
	Server   string `json:"server"`
	Port     int    `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	Database string `json:"database"`
	Timeout  int    `json:"timeoutSeconds"` // 连接和操作超时（秒）
}

// LineControllerConfig 定义了一条生产线/控制器的基本配置 (与旧项目保持一致)
type LineControllerConfig struct {
	LineCode         string `json:"lineCode"`
	LineName         string `json:"lineName"`
	ProductionLineId int    `json:"productionLineId"`
	PlcIpAddress     string `json:"plcIpAddress"`
	PlcPort          int    `json:"plcPort"`
	PlcSlaveId       byte   `json:"plcSlaveId"`
	IsEnabled        bool   `json:"isEnabled"`
}

// PollingRuntimeConfig 定义了特定生产线的轮询行为参数
type PollingRuntimeConfig struct {
	LineCode                   string `json:"lineCode"`
	PollingIntervalSec         int    `json:"pollingIntervalSec"`         // 正常轮询间隔（秒）
	FailureThreshold           int    `json:"failureThreshold"`           // 连续失败多少次后，认为连接真正中断 (用于进入首次退避或告警)
	BackoffIntervalsSec        []int  `json:"backoffIntervalsSec"`        // 退避间隔列表（秒），定义了各个退避级别的时间间隔
	FailuresToNextBackoffLevel []int  `json:"failuresToNextBackoffLevel"` // 每个退避级别对应的连续失败次数阈值，用于升级到下一级别。长度应与BackoffIntervalsSec匹配。
	MaxConsecutiveFailures     int    `json:"maxConsecutiveFailures"`     // 总的最大连续失败次数，之后可能需要特殊处理或告警
	// ErrorsInLevelToBackoff int `json:"errorsInLevelToBackoff"` // 已被 FailuresToNextBackoffLevel 替代
	UpsertTimeoutSec         int `json:"upsertTimeoutSec"`         // 数据库Upsert操作的超时时间（秒）
	ForceHighSpeedStartHour  int `json:"forceHighSpeedStartHour"`  // 强制高速轮询开始小时 (0-23)
	ForceHighSpeedEndHour    int `json:"forceHighSpeedEndHour"`    // 强制高速轮询结束小时 (0-23)
	ForceHighSpeedIntervalMs int `json:"forceHighSpeedIntervalMs"` // 强制高速轮询的间隔（毫秒）
	ConnectTimeoutMs         int `json:"connectTimeoutMs"`         // Modbus连接超时（毫秒）
	ResponseTimeoutMs        int `json:"responseTimeoutMs"`        // Modbus响应超时（毫秒）
}

// SchedulerConfig 定义了调度器相关的配置
type SchedulerConfig struct {
	Location               string                `json:"location"`               // 时区名称，例如 "Asia/Shanghai"
	Tasks                  []ScheduledTaskConfig `json:"tasks"`                  // 计划任务列表
	DefaultPollIntervalSec int                   `json:"defaultPollIntervalSec"` // 调度器检查任务的默认轮询间隔（秒），以防没有近期任务
}

// ScheduledTaskConfig 定义了一个具体的计划任务配置
type ScheduledTaskConfig struct {
	ID              string      `json:"id"`                // 任务的唯一标识
	Description     string      `json:"description"`       // 任务描述
	Hour            int         `json:"hour"`              // 触发小时 (0-23)
	Minute          int         `json:"minute"`            // 触发分钟 (0-59)
	Second          int         `json:"second"`            // 触发秒 (0-59)
	TaskMessageType string      `json:"taskMessageType"`   // 对应 messages.TaskType
	TargetChannel   string      `json:"targetChannelName"` // 目标Channel的逻辑名称 (用于 main.go 中映射到实际channel)
	Payload         interface{} `json:"payload"`           // JSON格式的任务负载
	OneTime         bool        `json:"oneTime"`           // 是否是一次性任务
}

// LoggingConfig 定义了日志相关的配置
type LoggingConfig struct {
	Level         string `json:"level"`         // 全局日志级别 (例如 DEBUG, INFO, WARNING, ERROR)
	LogToConsole  bool   `json:"logToConsole"`  // 是否输出日志到控制台
	LogToFile     bool   `json:"logToFile"`     // 是否输出日志到文件 (如果为true, 下面文件参数有效)
	LogPath       string `json:"logPath"`       // 日志文件路径
	LogFilename   string `json:"logFilename"`   // 日志文件名
	LogMaxSizeMB  int    `json:"logMaxSizeMB"`  // 单个日志文件最大大小 (MB)
	LogMaxBackups int    `json:"logMaxBackups"` // 最大备份日志文件数量
	LogMaxAgeDays int    `json:"logMaxAgeDays"` // 日志文件最大保留天数
}

// LoadConfig 从指定路径加载JSON配置文件并解析到 AppConfig 结构体
func LoadConfig(filePath string) (*AppConfig, error) {
	configFile, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var appConfig AppConfig
	err = json.Unmarshal(configFile, &appConfig)
	if err != nil {
		return nil, err
	}

	// Override config with environment variables
	overrideWithEnvVars(&appConfig)

	// 设置默认值或进行配置校验
	if appConfig.Database.Timeout <= 0 {
		appConfig.Database.Timeout = 30 // 默认30秒数据库超时
	}
	if appConfig.Scheduler.Location == "" {
		appConfig.Scheduler.Location = "Local" // 默认本地时区，但建议在配置中明确指定
	}
	if appConfig.Logging.Level == "" {
		appConfig.Logging.Level = "INFO"
	}

	for i := range appConfig.PollingRuntimes {
		pollingConfig := &appConfig.PollingRuntimes[i] // 获取指针以便修改
		if pollingConfig.ConnectTimeoutMs <= 0 {
			pollingConfig.ConnectTimeoutMs = 2000 // 默认 Modbus 连接超时 2000 毫秒
		}
		if pollingConfig.ResponseTimeoutMs <= 0 {
			pollingConfig.ResponseTimeoutMs = 1000 // 默认 Modbus 响应超时 1000 毫秒
		}
		if pollingConfig.PollingIntervalSec <= 0 {
			if pollingConfig.ForceHighSpeedIntervalMs <= 0 {
				pollingConfig.PollingIntervalSec = 1 // 默认至少1秒
			} else {
				// 如果有高速轮询，普通轮询间隔也应有值，这里也设为1秒，确保不为0
				// 实际逻辑中，普通轮询间隔应大于高速轮询间隔
				pollingConfig.PollingIntervalSec = 1
			}
		}

		// 校验 BackoffIntervalsSec 和 FailuresToNextBackoffLevel
		if len(pollingConfig.BackoffIntervalsSec) > 0 {
			if len(pollingConfig.FailuresToNextBackoffLevel) == 0 {
				// 如果定义了退避间隔但没有定义对应的失败次数，则为每个级别设置默认失败10次
				log.Printf("[config-%s]: FailuresToNextBackoffLevel 未定义。默认每个级别失败10次。", pollingConfig.LineCode)
				pollingConfig.FailuresToNextBackoffLevel = make([]int, len(pollingConfig.BackoffIntervalsSec))
				for j := range pollingConfig.FailuresToNextBackoffLevel {
					pollingConfig.FailuresToNextBackoffLevel[j] = 10 // 默认每个级别失败10次
				}
			} else if len(pollingConfig.FailuresToNextBackoffLevel) != len(pollingConfig.BackoffIntervalsSec) {
				log.Printf("[config-%s]: FailuresToNextBackoffLevel (数量 %d) 与 BackoffIntervalsSec (数量 %d) 的长度不匹配。在逻辑中可能会隐式进行截断或用默认值 (10) 填充，请谨慎使用。", pollingConfig.LineCode, len(pollingConfig.FailuresToNextBackoffLevel), len(pollingConfig.BackoffIntervalsSec))
				// 实际使用时，ModbusReader应以较短的那个长度为准，或明确处理此不匹配情况
				// 这里可以考虑强制使其长度一致，例如截断长的或用默认值填充短的
				// 目前这仅是一个警告。ModbusReader 的逻辑需要能够妥善处理这种不匹配的情况。
			}
		} else {
			// 如果没有定义 BackoffIntervalsSec，那么 FailuresToNextBackoffLevel 也就没有意义了
			pollingConfig.FailuresToNextBackoffLevel = nil
		}

		if pollingConfig.FailureThreshold <= 0 {
			pollingConfig.FailureThreshold = 5 // 默认首次进入退避的失败阈值为5次
		}
	}

	return &appConfig, nil
}

// overrideWithEnvVars uses environment variables to override configuration settings.
func overrideWithEnvVars(config *AppConfig) {
	if server := os.Getenv("DB_SERVER"); server != "" {
		config.Database.Server = server
	}
	if portStr := os.Getenv("DB_PORT"); portStr != "" {
		if port, err := strconv.Atoi(portStr); err == nil {
			config.Database.Port = port
		}
	}
	if user := os.Getenv("DB_USER"); user != "" {
		config.Database.User = user
	}
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Database.Password = password
	}
	if dbname := os.Getenv("DB_NAME"); dbname != "" {
		config.Database.Database = dbname
	}
}

// GetPollingRuntimeForLine 根据 lineCode 查找对应的 PollingRuntimeConfig
func (ac *AppConfig) GetPollingRuntimeForLine(lineCode string) *PollingRuntimeConfig {
	for i := range ac.PollingRuntimes {
		if ac.PollingRuntimes[i].LineCode == lineCode {
			return &ac.PollingRuntimes[i]
		}
	}
	return nil // 或者返回一个默认配置
}
