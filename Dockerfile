# docker run -d \
# -e "DB_SERVER=your_database_host" \
# -e "DB_PORT=1433" \
# -e "DB_USER=your_username" \
# -e "DB_PASSWORD=your_secret_password" \
# -e "DB_NAME=your_database_name" \
# --name my-modbus-app \
# modbus-server:latest

# Stage 1: Build the Go binary
FROM golang:1.23-alpine AS builder

# This is crucial for Go dependency downloads within Docker, especially in certain regions.
ENV GO111MODULE=on
ENV GOPROXY=https://goproxy.cn,direct

# Set the initial working directory for the builder stage
WORKDIR /app

# Install necessary build tools for static linking (if needed, alpine usually has them)
# RUN apk add --no-cache gcc musl-dev

# Copy go.mod and go.sum files first to leverage Docker cache for dependencies
# These will be copied to /app/go.mod and /app/go.sum
COPY go.mod go.sum ./

# Download dependencies in /app
RUN go mod download

# Now, copy the entire source code into /app
# The v1 code will be at /app/v1/
COPY . .

# Set build WORKDIR to the v1 application's root, which is now /app/v1
WORKDIR /app/v1

# Build the Go application (v1/main.go) statically for Linux AMD64
# CGO_ENABLED=0 ensures static linking without C dependencies (like glibc)
# -ldflags="-w -s" strips debug info and symbol table for smaller binary size
# Output the binary to a known location, e.g., /app/ (one level up from current WORKDIR)
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o /app/v1_main main.go

# Stage 2: Create the final minimal image
FROM alpine:latest

# Install tzdata for timezone support
RUN apk add --no-cache tzdata

# Set timezone to Asia/Shanghai
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Set the working directory for the final application
WORKDIR /app

# Create the directory for the v1 configuration file
RUN mkdir -p /app/v1/config

# Copy the v1 configuration file from the host
# Ensure this path matches where your v1 config is relative to the Docker build context root
COPY v1/config/config.json /app/v1/config/config.json

# (Optional) Create logs directory if your v1 app logs to ./logs relative to its execution
# RUN mkdir -p /app/v1/logs 

# Copy the static binary from the builder stage
# The binary was built at /app/v1_main in the builder stage
COPY --from=builder /app/v1_main /app/v1_main

# (Optional) Add CA certificates if your application needs to make HTTPS calls
# RUN apk add --no-cache ca-certificates

# Set the entrypoint for the container to run the v1 application
ENTRYPOINT ["/app/v1_main"] 