package utils

import (
	"log"
	"time"

	"modbus-app/backend/v1/messages"
)

// CumulativeDataHandler encapsulates the state and logic for processing,
// calculating cumulative values, and sending data for a reader.
type CumulativeDataHandler struct {
	// --- Configuration & Channels ---
	lineCode      string
	dataWriteChan chan<- messages.RealtimeDataMessage
	logger        *Logger // Use the new centralized logger

	// --- State variables for cumulative calculation and PLC reset handling ---
	dailyCumulativeInput  int
	appLastKnownPLCInput  int
	dailyCumulativeOutput int
	appLastKnownPLCOutput int

	// --- New: Daily baseline and reset compensation management ---
	dailyBaselineInput      int       // 当天0点时的PLC基准值
	dailyBaselineOutput     int       // 当天0点时的PLC基准值
	resetCompensationInput  int       // 设备重置补偿值
	resetCompensationOutput int       // 设备重置补偿值
	currentBusinessDate     time.Time // 当前业务日期

	// --- State for sending data only on change ---
	lastSentInput  int
	lastSentOutput int
}

// NewCumulativeDataHandler creates and initializes a new CumulativeDataHandler.
func NewCumulativeDataHandler(
	lineCode string,
	initialCumulativeInput, initialAppLastKnownPLCInput int,
	initialCumulativeOutput, initialAppLastKnownPLCOutput int,
	initialBaselineInput, initialBaselineOutput int,
	initialCompensationInput, initialCompensationOutput int,
	dataWriteChan chan<- messages.RealtimeDataMessage,
	logger *Logger, // Accept the logger instance
) *CumulativeDataHandler {
	return &CumulativeDataHandler{
		lineCode:                lineCode,
		dataWriteChan:           dataWriteChan,
		logger:                  logger, // Store the logger instance
		dailyCumulativeInput:    initialCumulativeInput,
		appLastKnownPLCInput:    initialAppLastKnownPLCInput,
		dailyCumulativeOutput:   initialCumulativeOutput,
		appLastKnownPLCOutput:   initialAppLastKnownPLCOutput,
		dailyBaselineInput:      initialBaselineInput,
		dailyBaselineOutput:     initialBaselineOutput,
		resetCompensationInput:  initialCompensationInput,
		resetCompensationOutput: initialCompensationOutput,
		currentBusinessDate:     time.Now().Truncate(24 * time.Hour),
		lastSentInput:           initialCumulativeInput,
		lastSentOutput:          initialCumulativeOutput,
	}
}

// ProcessReadData handles the logic after a successful PLC data read.
// It calculates cumulative values and sends data if it has changed.
func (h *CumulativeDataHandler) ProcessReadData(
	plcRawInput, plcRawOutput int,
	plcRawInputTime, plcRawOutputTime int,
	inputAddr, outputAddr string, // Pass configured addresses to decide if a value should be processed
) {
	log.Printf("[%s] 成功读取PLC原始数据: Input=%d, Output=%d. 上次PLC值: Input=%d, Output=%d",
		h.logger.source, plcRawInput, plcRawOutput, h.appLastKnownPLCInput, h.appLastKnownPLCOutput)

	// 使用-999表示"不更新此字段"，-1及其他值都是有效值
	finalInput := -999
	finalOutput := -999

	// Skip calculation if configured raw values are unchanged
	shouldSkip := true
	if inputAddr != "" && (h.appLastKnownPLCInput == -1 || plcRawInput != h.appLastKnownPLCInput) {
		shouldSkip = false
	}
	if outputAddr != "" && (h.appLastKnownPLCOutput == -1 || plcRawOutput != h.appLastKnownPLCOutput) {
		shouldSkip = false
	}

	if shouldSkip {
		log.Printf("[%s] 所有已配置的PLC原始值均未改变，跳过累计值计算。", h.logger.source)
	} else {
		// Process Input if configured
		if inputAddr != "" {
			h.dailyCumulativeInput, h.appLastKnownPLCInput = CalculateCumulativeWithBaseline(
				h.lineCode, "Input",
				h.dailyCumulativeInput, h.appLastKnownPLCInput, plcRawInput,
				&h.dailyBaselineInput, &h.resetCompensationInput,
			)
		}
		// Process Output if configured
		if outputAddr != "" {
			h.dailyCumulativeOutput, h.appLastKnownPLCOutput = CalculateCumulativeWithBaseline(
				h.lineCode, "Output",
				h.dailyCumulativeOutput, h.appLastKnownPLCOutput, plcRawOutput,
				&h.dailyBaselineOutput, &h.resetCompensationOutput,
			)
		}
	}

	// Always update to the latest cumulative values for the message
	if inputAddr != "" {
		finalInput = h.dailyCumulativeInput
	}
	if outputAddr != "" {
		finalOutput = h.dailyCumulativeOutput
	}

	// Send data only if cumulative values have changed since last send
	// 注意：-999表示不更新，不应该参与比较
	inputChanged := (finalInput != -999) && (finalInput != h.lastSentInput)
	outputChanged := (finalOutput != -999) && (finalOutput != h.lastSentOutput)

	if inputChanged || outputChanged {
		dataMsg := messages.RealtimeDataMessage{
			BaseMessage:             messages.BaseMessage{Type: messages.TaskRealtimeData, Timestamp: time.Now()},
			LineCode:                h.lineCode,
			RealDatetime:            time.Now(),
			Input:                   finalInput,
			Output:                  finalOutput,
			InputTime:               plcRawInputTime,  // Pass through raw time/beat values
			OutputTime:              plcRawOutputTime, // Pass through raw time/beat values
			AppLastKnownPLCInput:    h.appLastKnownPLCInput,
			AppLastKnownPLCOutput:   h.appLastKnownPLCOutput,
			DailyBaselineInput:      h.dailyBaselineInput,
			DailyBaselineOutput:     h.dailyBaselineOutput,
			ResetCompensationInput:  h.resetCompensationInput,
			ResetCompensationOutput: h.resetCompensationOutput,
		}
		select {
		case h.dataWriteChan <- dataMsg:
			log.Printf("[%s] 已发送数据。新值: {Input:%d, Output:%d}, 上次发送: {Input:%d, Output:%d}",
				h.logger.source, finalInput, finalOutput, h.lastSentInput, h.lastSentOutput)
			// 只更新实际发送的字段
			if finalInput != -999 {
				h.lastSentInput = finalInput
			}
			if finalOutput != -999 {
				h.lastSentOutput = finalOutput
			}
		case <-time.After(2 * time.Second):
			log.Printf("[%s-警告] 发送数据到通道超时。Input:%d, Output:%d", h.logger.source, finalInput, finalOutput)
			h.logger.Send(messages.SeverityWarning, "发送数据到通道超时", nil)
		}
	} else {
		log.Printf("[%s] 计算后的值与上次发送的相同，本次不发送数据。Input:%d, Output:%d", h.logger.source, finalInput, finalOutput)
	}
}

// HandleCommand processes commands received from the command channel.
func (h *CumulativeDataHandler) HandleCommand(cmd interface{}) {
	log.Printf("[%s] 收到命令，类型: %T", h.logger.source, cmd)
	switch cmd.(type) {
	case messages.MidnightResetMessage:
		h.handleMidnightReset()
	case messages.ForceSendLastKnownDataMessage:
		h.handleForceSend()
	default:
		log.Printf("[%s] 收到未知类型的命令: %T - %+v", h.logger.source, cmd, cmd)
	}
}

func (h *CumulativeDataHandler) handleMidnightReset() {
	log.Printf("[%s] ===== 开始处理午夜重置命令 =====", h.logger.source)
	log.Printf("[%s] 重置前状态: Input累计=%d, Output累计=%d, Input基准=%d, Output基准=%d, InputPLC=%d, OutputPLC=%d, Input补偿=%d, Output补偿=%d",
		h.logger.source, h.dailyCumulativeInput, h.dailyCumulativeOutput, h.dailyBaselineInput, h.dailyBaselineOutput,
		h.appLastKnownPLCInput, h.appLastKnownPLCOutput, h.resetCompensationInput, h.resetCompensationOutput)

	// 强制认为PLC重启会归0，所以午夜重置时基准值设为-1，等待第一次读取时设置
	// 这样可以处理设备关机重启后PLC归0的情况
	log.Printf("[%s] 午夜重置：强制将基准值设为-1，等待设备重启后第一次读取", h.logger.source)
	h.dailyBaselineInput = -1  // 等待第一次读取时设置为实际PLC值（可能是0）
	h.dailyBaselineOutput = -1 // 等待第一次读取时设置为实际PLC值（可能是0）

	// 重置累计值和补偿值
	h.dailyCumulativeInput = 0
	h.dailyCumulativeOutput = 0
	h.resetCompensationInput = 0
	h.resetCompensationOutput = 0

	// 关键修复：重置PLC值为-1，强制新一天重新读取和建立基准值关系
	h.appLastKnownPLCInput = -1
	h.appLastKnownPLCOutput = -1

	// 更新业务日期
	h.currentBusinessDate = time.Now().Truncate(24 * time.Hour)

	log.Printf("[%s] 重置后状态: Input累计=%d, Output累计=%d, Input基准=%d, Output基准=%d, InputPLC=%d, OutputPLC=%d, Input补偿=%d, Output补偿=%d",
		h.logger.source, h.dailyCumulativeInput, h.dailyCumulativeOutput, h.dailyBaselineInput, h.dailyBaselineOutput,
		h.appLastKnownPLCInput, h.appLastKnownPLCOutput, h.resetCompensationInput, h.resetCompensationOutput)

	resetDataMsg := messages.RealtimeDataMessage{
		BaseMessage:             messages.BaseMessage{Type: messages.TaskRealtimeData, Timestamp: time.Now()},
		LineCode:                h.lineCode,
		RealDatetime:            time.Now(),
		Input:                   h.dailyCumulativeInput,
		Output:                  h.dailyCumulativeOutput,
		AppLastKnownPLCInput:    h.appLastKnownPLCInput,
		AppLastKnownPLCOutput:   h.appLastKnownPLCOutput,
		DailyBaselineInput:      h.dailyBaselineInput,
		DailyBaselineOutput:     h.dailyBaselineOutput,
		ResetCompensationInput:  h.resetCompensationInput,
		ResetCompensationOutput: h.resetCompensationOutput,
	}

	log.Printf("[%s] 准备写入午夜重置数据: Input=%d, Output=%d, LastPLCInput=%d, LastPLCOutput=%d, BaselineInput=%d, BaselineOutput=%d, CompensationInput=%d, CompensationOutput=%d",
		h.logger.source, resetDataMsg.Input, resetDataMsg.Output, resetDataMsg.AppLastKnownPLCInput, resetDataMsg.AppLastKnownPLCOutput,
		resetDataMsg.DailyBaselineInput, resetDataMsg.DailyBaselineOutput, resetDataMsg.ResetCompensationInput, resetDataMsg.ResetCompensationOutput)

	select {
	case h.dataWriteChan <- resetDataMsg:
		log.Printf("[%s] ===== 午夜重置数据已成功发送到数据库 =====", h.logger.source)
		h.lastSentInput = h.dailyCumulativeInput
		h.lastSentOutput = h.dailyCumulativeOutput
		h.logger.Send(messages.SeverityInfo, "午夜重置完成并已发送数据。", nil)
	case <-time.After(2 * time.Second):
		log.Printf("[%s-警告] 发送午夜重置数据到通道超时。", h.logger.source)
		h.logger.Send(messages.SeverityWarning, "发送午夜重置数据超时", nil)
	}
}

func (h *CumulativeDataHandler) handleForceSend() {
	log.Printf("[%s] 正在处理强制发送命令。发送上次已知累计数据: Input=%d, Output=%d",
		h.logger.source, h.lastSentInput, h.lastSentOutput)

	forcedDataMsg := messages.RealtimeDataMessage{
		BaseMessage:             messages.BaseMessage{Type: messages.TaskRealtimeData, Timestamp: time.Now()},
		LineCode:                h.lineCode,
		RealDatetime:            time.Now(),
		Input:                   h.lastSentInput,
		Output:                  h.lastSentOutput,
		AppLastKnownPLCInput:    h.appLastKnownPLCInput,
		AppLastKnownPLCOutput:   h.appLastKnownPLCOutput,
		DailyBaselineInput:      h.dailyBaselineInput,
		DailyBaselineOutput:     h.dailyBaselineOutput,
		ResetCompensationInput:  h.resetCompensationInput,
		ResetCompensationOutput: h.resetCompensationOutput,
	}
	select {
	case h.dataWriteChan <- forcedDataMsg:
		log.Printf("[%s] 已强制发送上次已知数据。Input:%d, Output:%d",
			h.logger.source, h.lastSentInput, h.lastSentOutput)
		h.logger.Send(messages.SeverityInfo, "强制发送上次已知数据完成。", nil)
	case <-time.After(2 * time.Second):
		log.Printf("[%s-警告] 强制发送上次已知数据到通道超时。", h.logger.source)
		h.logger.Send(messages.SeverityWarning, "强制发送上次已知数据超时", nil)
	}
}
