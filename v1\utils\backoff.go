package utils

import (
	"fmt"
	"log"
	"time"
)

// BackoffConfig defines the configuration for the BackoffController.
// This decouples the controller from specific reader configs.
type BackoffConfig struct {
	PollingInterval            time.Duration
	BackoffIntervalsSec        []int
	FailuresToNextBackoffLevel []int
}

// BackoffController manages the state and logic for an exponential backoff strategy.
type BackoffController struct {
	config                 BackoffConfig
	loggerPrefix           string
	currentBackoffLevel    int
	failuresInCurrentLevel int
	backoffUntil           time.Time
}

// NewBackoffController creates and initializes a new BackoffController.
func NewBackoffController(cfg BackoffConfig, loggerPrefix string) *BackoffController {
	if len(cfg.BackoffIntervalsSec) == 0 || len(cfg.FailuresToNextBackoffLevel) == 0 {
		log.Printf("[%s] 退避间隔或失败阈值为空。退避功能将无法正常工作。", loggerPrefix)
	}
	return &BackoffController{
		config:       cfg,
		loggerPrefix: loggerPrefix,
	}
}

// ShouldSkipCycle checks if the current polling cycle should be skipped due to an active backoff period.
func (bc *BackoffController) ShouldSkipCycle() bool {
	if !bc.backoffUntil.IsZero() {
		if time.Now().Before(bc.backoffUntil) {
			// Log only once when deciding to skip
			// log.Printf("[%s] Currently in backoff state until %s. Skipping this polling cycle.", bc.loggerPrefix, bc.backoffUntil.Format(time.RFC3339))
			return true
		}
		log.Printf("[%s] 退避期已结束。恢复轮询周期。", bc.loggerPrefix)
		bc.backoffUntil = time.Time{}
	}
	return false
}

// HandleSuccess resets the backoff state after a successful operation.
func (bc *BackoffController) HandleSuccess() {
	if bc.currentBackoffLevel > 0 || bc.failuresInCurrentLevel > 0 {
		log.Printf("[%s] 操作成功。重置退避级别，从 %d 降至 0。", bc.loggerPrefix, bc.currentBackoffLevel)
	}
	bc.currentBackoffLevel = 0
	bc.failuresInCurrentLevel = 0
	bc.backoffUntil = time.Time{}
}

// HandleFailure updates the backoff state after a failed operation.
func (bc *BackoffController) HandleFailure() {
	bc.failuresInCurrentLevel++

	// Check if we need to advance to the next backoff level
	if bc.currentBackoffLevel < len(bc.config.FailuresToNextBackoffLevel) &&
		bc.failuresInCurrentLevel >= bc.config.FailuresToNextBackoffLevel[bc.currentBackoffLevel] {
		// Advance to the next level if we are not already at the max level
		if bc.currentBackoffLevel < len(bc.config.BackoffIntervalsSec)-1 {
			previousThreshold := bc.config.FailuresToNextBackoffLevel[bc.currentBackoffLevel]
			bc.currentBackoffLevel++
			log.Printf("[%s] 由于前一级别失败 %d 次，已提升至退避级别 %d。",
				bc.loggerPrefix, previousThreshold, bc.currentBackoffLevel)
		} else {
			log.Printf("[%s] 已达到最大退避级别 %d，但失败阈值已满足。将继续使用最大退避间隔。",
				bc.loggerPrefix, bc.currentBackoffLevel)
		}
		// Reset the failure counter for the new level
		bc.failuresInCurrentLevel = 0
	}

	// Apply the backoff duration for the current level
	if bc.currentBackoffLevel < len(bc.config.BackoffIntervalsSec) {
		backoffDuration := time.Duration(bc.config.BackoffIntervalsSec[bc.currentBackoffLevel]) * time.Second
		bc.backoffUntil = time.Now().Add(backoffDuration)
		log.Printf("[%s] 操作失败。进入退避状态至 %s (时长: %v)。级别: %d, 当前级别失败次数(递增后): %d。",
			bc.loggerPrefix, bc.backoffUntil.Format(time.RFC3339), backoffDuration, bc.currentBackoffLevel, bc.failuresInCurrentLevel)
	}
}

// ResetTicker adjusts the ticker's interval based on the current backoff level.
func (bc *BackoffController) ResetTicker(ticker *time.Ticker) {
	var newInterval time.Duration
	if bc.currentBackoffLevel >= 0 && bc.currentBackoffLevel < len(bc.config.BackoffIntervalsSec) {
		newInterval = time.Duration(bc.config.BackoffIntervalsSec[bc.currentBackoffLevel]) * time.Second
		if newInterval <= 0 {
			newInterval = bc.config.PollingInterval
		}
	} else {
		newInterval = bc.config.PollingInterval
	}

	ticker.Reset(newInterval)
	log.Printf("[%s] 定时器重置为 %v (退避级别: %d)", bc.loggerPrefix, newInterval, bc.currentBackoffLevel)
}

// GetStatusLine returns a string summarizing the current backoff status for logging.
func (bc *BackoffController) GetStatusLine() string {
	return fmt.Sprintf("退避状态: 级别=%d, 本级失败次数=%d, 退避至=%s",
		bc.currentBackoffLevel, bc.failuresInCurrentLevel, bc.backoffUntil.Format(time.RFC3339))
}
