package modbusreader

import (
	"fmt"
	"log"
	"strconv"
	"sync"
	"time"

	"modbus-app/backend/v1/messages"
	"modbus-app/backend/v1/utils" // 导入新的公共包

	// "modbus-app/backend/v1/datastore" // datastore.ManagedLineInfo will be part of CombinedReaderConfig

	modbus "github.com/simonvetter/modbus" // Using the same library as in old client.go
)

// CombinedReaderConfig holds all necessary runtime configurations for a ModbusReader instance.
// This is assembled by main.go from datastore.ManagedLineInfo and config.PollingRuntimeConfig.
type CombinedReaderConfig struct {
	LineCode                   string
	ModbusIP                   string
	ModbusPort                 int
	PlcSlaveID                 int
	ConnectTimeout             time.Duration
	ResponseTimeout            time.Duration
	PollingInterval            time.Duration
	FailureThreshold           int
	BackoffIntervalsSec        []int
	FailuresToNextBackoffLevel []int
	MaxConsecutiveFailures     int

	// Addresses are now strings to be parsed, matching the database schema
	InputAddress      string
	InputTimeAddress  string
	OutputAddress     string
	OutputTimeAddress string
}

// ModbusReader handles communication with a single Modbus device.
type ModbusReader struct {
	config          CombinedReaderConfig
	client          *modbus.ModbusClient
	slaveIDByte     byte
	shutdownChannel <-chan struct{}
	commandChannel  <-chan interface{}
	logger          *utils.Logger

	// All data and state logic is now delegated to the handler
	dataHandler *utils.CumulativeDataHandler
}

// NewModbusReader creates and initializes a new ModbusReader.
func NewModbusReader(
	cfg CombinedReaderConfig,
	initialCumulativeInput int, initialAppLastKnownPLCInput int,
	initialCumulativeOutput int, initialAppLastKnownPLCOutput int,
	initialBaselineInput int, initialBaselineOutput int,
	initialCompensationInput int, initialCompensationOutput int,
	errorLogChan chan<- messages.LogErrorMessage,
	dataWriteChan chan<- messages.RealtimeDataMessage,
	shutdownChannel <-chan struct{},
	commandChannel <-chan interface{},
) (*ModbusReader, error) {
	log.Printf("[ModbusReader-%s] NewModbusReader: 正在为 %s:%d (从站ID: %d) 初始化", cfg.LineCode, cfg.ModbusIP, cfg.ModbusPort, cfg.PlcSlaveID)

	if errorLogChan == nil || dataWriteChan == nil || shutdownChannel == nil || commandChannel == nil {
		return nil, fmt.Errorf("NewModbusReader (产线 %s): channel 参数不能为 nil", cfg.LineCode)
	}

	if cfg.PlcSlaveID < 0 || cfg.PlcSlaveID > 255 {
		errMsg := fmt.Sprintf("NewModbusReader (产线 %s): 无效的 PLC 从站ID %d. 必须在 0 到 255 之间.", cfg.LineCode, cfg.PlcSlaveID)
		// Cannot use the final logger here as it's not initialized yet.
		// Log directly and return.
		log.Printf("[ModbusReader-%s] %s", cfg.LineCode, errMsg)
		return nil, fmt.Errorf(errMsg)
	}
	slaveIDAsByte := byte(cfg.PlcSlaveID)

	clientURL := fmt.Sprintf("tcp://%s:%d", cfg.ModbusIP, cfg.ModbusPort)
	modbusClientCfg := &modbus.ClientConfiguration{
		URL:     clientURL,
		Timeout: cfg.ResponseTimeout,
	}

	mbClient, err := modbus.NewClient(modbusClientCfg)
	if err != nil {
		// Cannot use the final logger here as it's not initialized yet.
		log.Printf("[ModbusReader-%s] 为 %s 创建 Modbus 客户端实例失败: %v", cfg.LineCode, clientURL, err)
		return nil, fmt.Errorf("NewModbusReader (产线 %s): 为 %s 创建 Modbus 客户端实例失败: %v", cfg.LineCode, clientURL, err)
	}

	// Setup the centralized logger with default context for this reader
	source := fmt.Sprintf("ModbusReader-%s", cfg.LineCode)
	defaultLogDetails := map[string]interface{}{
		"line_code":       cfg.LineCode,
		"modbus_ip":       cfg.ModbusIP,
		"modbus_port":     cfg.ModbusPort,
		"modbus_slave_id": cfg.PlcSlaveID,
	}
	logger := utils.NewLogger(errorLogChan, source, defaultLogDetails)

	// Initialize the data handler which will manage all state, passing the logger to it
	dataHandler := utils.NewCumulativeDataHandler(
		cfg.LineCode,
		initialCumulativeInput, initialAppLastKnownPLCInput,
		initialCumulativeOutput, initialAppLastKnownPLCOutput,
		initialBaselineInput, initialBaselineOutput,
		initialCompensationInput, initialCompensationOutput,
		dataWriteChan,
		logger, // Pass the created logger
	)

	mr := &ModbusReader{
		config:          cfg,
		client:          mbClient,
		slaveIDByte:     slaveIDAsByte,
		shutdownChannel: shutdownChannel,
		commandChannel:  commandChannel,
		dataHandler:     dataHandler,
		logger:          logger,
	}

	log.Printf(
		"[ModbusReader-%s] NewModbusReader: 初始化成功. Data handler initialized.",
		cfg.LineCode,
	)
	return mr, nil
}

// Start begins the Modbus polling loop.
func (mr *ModbusReader) Start(wg *sync.WaitGroup) {
	log.Printf("[ModbusReader-%s] 启动轮询循环...", mr.config.LineCode)

	// 注意：wg.Add(1)已在调用方(main.go)中完成，这里不需要重复调用
	go func() {
		defer wg.Done()
		defer func() {
			if mr.client != nil {
				log.Printf("[ModbusReader-%s] defer函数中尝试关闭 Modbus 连接...", mr.config.LineCode)
				mr.client.Close()
			}
			log.Printf("[ModbusReader-%s] 轮询循环已停止。", mr.config.LineCode)
		}()

		// Initialize the new BackoffController
		backoffController := utils.NewBackoffController(utils.BackoffConfig{
			PollingInterval:            mr.config.PollingInterval,
			BackoffIntervalsSec:        mr.config.BackoffIntervalsSec,
			FailuresToNextBackoffLevel: mr.config.FailuresToNextBackoffLevel,
		}, fmt.Sprintf("ModbusReader-%s", mr.config.LineCode))

		var isConnected bool
		ticker := time.NewTicker(mr.config.PollingInterval)
		defer ticker.Stop()
		backoffController.ResetTicker(ticker) // Initial ticker setup

		for {
			select {
			case <-mr.shutdownChannel:
				log.Printf("[ModbusReader-%s] 收到关闭信号，正在停止轮询循环。", mr.config.LineCode)
				return

			case cmd, ok := <-mr.commandChannel:
				if !ok {
					log.Printf("[ModbusReader-%s] 命令通道已关闭，可能正在关闭。", mr.config.LineCode)
					continue
				}
				log.Printf("[ModbusReader-%s] 从命令通道收到消息，类型: %T", mr.config.LineCode, cmd)
				mr.dataHandler.HandleCommand(cmd)

			case <-ticker.C:
				log.Printf("[ModbusReader-%s] 轮询信号。%s",
					mr.config.LineCode, backoffController.GetStatusLine())

				if backoffController.ShouldSkipCycle() {
					continue
				}

				var operationError error

				// 1. Ensure connection
				if !isConnected {
					log.Printf("[ModbusReader-%s] 未连接。尝试连接到 %s:%d...", mr.config.LineCode, mr.config.ModbusIP, mr.config.ModbusPort)
					err := mr.client.Open()
					if err != nil {
						log.Printf("[ModbusReader-%s] 连接失败: %v", mr.config.LineCode, err)
						isConnected = false
						operationError = err // 将连接错误赋给 operationError
						mr.logger.Send(messages.SeverityError, fmt.Sprintf("连接失败: %v", err), nil)
					} else {
						log.Printf("[ModbusReader-%s] 连接成功。", mr.config.LineCode)
						isConnected = true
					}
				}

				// 2. Read data only if connected and no error has occurred yet.
				var plcRawInput, plcRawOutput, plcRawInputTime, plcRawOutputTime int
				if isConnected && operationError == nil {
					mr.client.SetUnitId(mr.slaveIDByte)
					log.Printf("[ModbusReader-%s] 正在读取数据...", mr.config.LineCode)
					var readError error

					// Helper to read a single register. Handles different register types based on address.
					readReg := func(addrStr string) (int, error) {
						if addrStr == "" {
							return 0, nil // No address means no value, not an error.
						}
						addr, err := strconv.Atoi(addrStr)
						if err != nil {
							return 0, fmt.Errorf("无效的地址格式 '%s': %w", addrStr, err)
						}

						var actualAddress uint16

						// Determine register type and address based on standard Modbus conventions.
						if addr >= 40001 && addr <= 49999 { // Holding Registers (FC3)
							actualAddress = uint16(addr - 40001)
							regs, err := mr.client.ReadRegisters(actualAddress, 1, modbus.HOLDING_REGISTER)
							if err != nil {
								return 0, fmt.Errorf("从地址 %s (物理地址 %d) 读取保持寄存器失败: %w", addrStr, actualAddress, err)
							}
							if len(regs) < 1 {
								return 0, fmt.Errorf("从地址 %s 读取未返回任何数据", addrStr)
							}
							return int(regs[0]), nil
						} else if addr >= 30001 && addr <= 39999 { // Input Registers (FC4)
							actualAddress = uint16(addr - 30001)
							regs, err := mr.client.ReadRegisters(actualAddress, 1, modbus.INPUT_REGISTER)
							if err != nil {
								return 0, fmt.Errorf("从地址 %s (物理地址 %d) 读取输入寄存器失败: %w", addrStr, actualAddress, err)
							}
							if len(regs) < 1 {
								return 0, fmt.Errorf("从地址 %s 读取未返回任何数据", addrStr)
							}
							return int(regs[0]), nil
						} else if addr >= 1 && addr <= 9999 { // Coils (FC1)
							actualAddress = uint16(addr - 1)
							bits, err := mr.client.ReadCoils(actualAddress, 1)
							if err != nil {
								return 0, fmt.Errorf("从地址 %s (物理地址 %d) 读取线圈失败: %w", addrStr, actualAddress, err)
							}
							if len(bits) < 1 {
								return 0, fmt.Errorf("从地址 %s 读取未返回任何数据", addrStr)
							}
							if bits[0] {
								return 1, nil
							}
							return 0, nil
						} else if addr >= 10001 && addr <= 19999 { // Discrete Inputs (FC2)
							actualAddress = uint16(addr - 10001)
							bits, err := mr.client.ReadDiscreteInputs(actualAddress, 1)
							if err != nil {
								return 0, fmt.Errorf("从地址 %s (物理地址 %d) 读取离散输入失败: %w", addrStr, actualAddress, err)
							}
							if len(bits) < 1 {
								return 0, fmt.Errorf("从地址 %s 读取未返回任何数据", addrStr)
							}
							if bits[0] {
								return 1, nil
							}
							return 0, nil
						} else {
							return 0, fmt.Errorf("不支持或无效的 Modbus 地址 '%s'。支持的范围: 1-9999 (线圈), 10001-19999 (离散输入), 30001-39999 (输入寄存器), 40001-49999 (保持寄存器)", addrStr)
						}
					}

					// Read all required values sequentially. A failure in one stops the sequence.
					plcRawInput, readError = readReg(mr.config.InputAddress)
					if readError == nil {
						plcRawInputTime, readError = readReg(mr.config.InputTimeAddress)
					}
					if readError == nil {
						plcRawOutput, readError = readReg(mr.config.OutputAddress)
					}
					if readError == nil {
						plcRawOutputTime, readError = readReg(mr.config.OutputTimeAddress)
					}

					if readError != nil {
						log.Printf("[ModbusReader-%s] 处理 Modbus 数据时出错: %v", mr.config.LineCode, readError)
						isConnected = false
						mr.client.Close()
						operationError = readError // 将读取错误也赋给 operationError
						mr.logger.Send(messages.SeverityError, fmt.Sprintf("处理 Modbus 数据时出错: %v", readError), nil)
					}
				}

				// 3. Process data or handle failure
				if operationError == nil { // --- SUCCESS PATH ---
					// Delegate data processing to the handler
					mr.dataHandler.ProcessReadData(
						plcRawInput, plcRawOutput,
						plcRawInputTime, plcRawOutputTime,
						mr.config.InputAddress, mr.config.OutputAddress,
					)

					// On full success, notify the backoff controller
					backoffController.HandleSuccess()
					backoffController.ResetTicker(ticker)
				} else {
					// --- FAILURE PATH (unified backoff logic) ---
					backoffController.HandleFailure()
					backoffController.ResetTicker(ticker)
				}
			} // end select
		} // end for
	}() // end go func
} // end Start
