{"database": {"server": "10.100.100.100", "port": 1433, "user": "sa", "password": "pushiAIO8", "database": "AErp8_SZ_YJDJ", "timeoutSeconds": 30}, "pollingRuntimes": [{"lineCode": "DEFAULT", "pollingIntervalSec": 5, "failureThreshold": 3, "backoffIntervalsSec": [1, 10, 30, 60, 300, 1000], "failuresToNextBackoffLevel": [10, 30, 30, 20, 20, 20], "maxConsecutiveFailures": 20, "upsertTimeoutSec": 10, "forceHighSpeedStartHour": -1, "forceHighSpeedEndHour": -1, "forceHighSpeedIntervalMs": 0, "connectTimeoutMs": 2000, "responseTimeoutMs": 1000}], "scheduler": {"location": "Asia/Shanghai", "tasks": [{"id": "midnight_modbus_reset", "description": "午夜 Modbus 读取器缓存清零任务", "hour": 0, "minute": 0, "second": 0, "taskMessageType": "MIDNIGHT_RESET", "targetChannelName": "midnight_notifications", "payload": {}, "oneTime": false}, {"id": "hourly_force_send_modbus_last_known", "description": "每小时05分强制 Modbus 读取器发送其最近的已知数据", "hour": -1, "minute": 5, "second": 0, "taskMessageType": "HOURLY_FORCE_SEND", "targetChannelName": "hourly_force_send_notifications", "payload": {}, "oneTime": false}], "defaultPollIntervalSec": 60}, "logging": {"level": "INFO", "logToConsole": true, "logToFile": false, "logPath": "./logs", "logFilename": "modbus_app.log", "logMaxSizeMB": 100, "logMaxBackups": 3, "logMaxAgeDays": 7}}