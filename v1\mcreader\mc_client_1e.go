package mcreader

import (
	"context"
	"encoding/binary"
	"fmt"
	"io"
	"log"
	"net"
	"sync"
	"time"
)

// MC 为外部暴露的统一接口（A-1E/1E 框架，后续可扩展 3E）
// 设备地址均以 word 为单位（16bit）。
//
// 注意：本实现线程安全（内部互斥），可被多 goroutine 并发调用。
//
// ErrPLC 表示 PLC 返回的非零 Status/EndCode，可通过 errors.Is/As 检测。
// 遇到网络错误会尝试自动重连一次，再向上层返回错误。
//
// Create via NewClient。
type MC interface {
	ReadWords(ctx context.Context, dev string, addr, qty uint16) ([]uint16, error)
	WriteWords(ctx context.Context, dev string, addr uint16, vals []uint16) error
	Ping(ctx context.Context) error
	Close() error
}

type mcClient struct {
	mu          sync.Mutex
	conn        net.Conn
	addr        string
	dialTimeout time.Duration
	logger      *log.Logger
}

type Option func(*mcClient)

// WithLogger 允许注入自定义 logger；默认使用 log.Printf。
func WithLogger(l *log.Logger) Option {
	return func(c *mcClient) { c.logger = l }
}

// NewClient 建立到 PLC 的 TCP 连接并返回 MC 实例。
func NewClient(ip string, port int, dialTimeout time.Duration, opts ...Option) (MC, error) {
	c := &mcClient{
		addr:        fmt.Sprintf("%s:%d", ip, port),
		dialTimeout: dialTimeout,
		logger:      log.Default(),
	}
	for _, f := range opts {
		f(c)
	}
	// 首次建连
	if err := c.dial(); err != nil {
		return nil, err
	}
	return c, nil
}

// dial 打开连接并设置 KeepAlive
func (c *mcClient) dial() error {
	conn, err := net.DialTimeout("tcp", c.addr, c.dialTimeout)
	if err != nil {
		return err
	}
	if tc, ok := conn.(*net.TCPConn); ok {
		_ = tc.SetKeepAlive(true)
		_ = tc.SetKeepAlivePeriod(30 * time.Second)
	}
	c.conn = conn
	return nil
}

// ensureConn 若连接已失效则重连一次。
func (c *mcClient) ensureConn(ctx context.Context) error {
	if c.conn != nil {
		return nil
	}
	// 无锁调用，用于首次连接后因错误被置空的情况。
	return c.dial()
}

// Close 关闭底层连接
func (c *mcClient) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	if c.conn != nil {
		err := c.conn.Close()
		c.conn = nil
		return err
	}
	return nil
}

// Ping 发送健康检查 —— 这里简单复用 ReadWords 读取自身 D0 1word
func (c *mcClient) Ping(ctx context.Context) error {
	_, err := c.ReadWords(ctx, "D", 0, 1)
	return err
}

// ReadWords 实现按 word 读取
func (c *mcClient) ReadWords(ctx context.Context, dev string, addr, qty uint16) ([]uint16, error) {
	code, ok := devCode[dev]
	if !ok {
		return nil, fmt.Errorf("unsupported device %s", dev)
	}
	frame := buildReadFrame(code, addr, qty)

	// 预期响应：header2 + payload
	exp := int(qty)*2 + 2
	resp, err := c.transceive(ctx, frame, exp)
	if err != nil {
		return nil, err
	}
	if len(resp) < 2 || resp[0] != 0x81 {
		return nil, fmt.Errorf("unexpected response header: %X", resp)
	}
	status := resp[1]
	if status != 0 {
		return nil, PLCError{Code: uint16(status)}
	}
	data := resp[2:]
	out := make([]uint16, qty)
	for i := 0; i < int(qty); i++ {
		if 2*i+1 >= len(data) {
			return nil, fmt.Errorf("response length insufficient")
		}
		out[i] = binary.LittleEndian.Uint16(data[2*i:])
	}
	return out, nil
}

// WriteWords 实现按 word 写入
func (c *mcClient) WriteWords(ctx context.Context, dev string, addr uint16, vals []uint16) error {
	if len(vals) == 0 {
		return nil
	}
	code, ok := devCode[dev]
	if !ok {
		return fmt.Errorf("unsupported device %s", dev)
	}
	frame := buildWriteFrame(code, addr, vals)
	resp, err := c.transceive(ctx, frame, 2)
	if err != nil {
		return err
	}
	if len(resp) < 2 || resp[0] != 0x83 {
		return fmt.Errorf("unexpected response header: %X", resp)
	}
	status := resp[1]
	if status != 0 {
		return PLCError{Code: uint16(status)}
	}
	return nil
}

// transceive 负责发送并读取指定长度的响应；如遇网络错误会自动重连一次
func (c *mcClient) transceive(ctx context.Context, req []byte, respLen int) ([]byte, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 若 conn 为空或关闭则重连
	if err := c.ensureConn(ctx); err != nil {
		return nil, err
	}

	deadline, ok := ctx.Deadline()
	if ok {
		_ = c.conn.SetDeadline(deadline)
	}

	// 发送
	if _, err := c.conn.Write(req); err != nil {
		_ = c.resetConn()
		return nil, err
	}

	// 读取
	resp := make([]byte, respLen)
	if _, err := io.ReadFull(c.conn, resp); err != nil {
		_ = c.resetConn()
		return nil, err
	}
	return resp, nil
}

// resetConn 在网络错误后关闭连接，供下一次 ensureConn 重连
func (c *mcClient) resetConn() error {
	if c.conn != nil {
		_ = c.conn.Close()
		c.conn = nil
	}
	return nil
}

// ===== 1E 包装函数 / 方法 =====
// 目的是向外暴露带 "1E" 后缀的 API，同时内部复用已有实现，保持兼容性。

// NewClient1E 行为与 NewClient 完全一致，仅在命名上标识 1E 协议。
func NewClient1E(ip string, port int, dialTimeout time.Duration, opts ...Option) (MC, error) {
	return NewClient(ip, port, dialTimeout, opts...)
}

// ReadWords1E 包装 mcClient.ReadWords
func (c *mcClient) ReadWords1E(ctx context.Context, dev string, addr, qty uint16) ([]uint16, error) {
	return c.ReadWords(ctx, dev, addr, qty)
}

// WriteWords1E 包装 mcClient.WriteWords
func (c *mcClient) WriteWords1E(ctx context.Context, dev string, addr uint16, vals []uint16) error {
	return c.WriteWords(ctx, dev, addr, vals)
}

// Ping1E 包装 mcClient.Ping
func (c *mcClient) Ping1E(ctx context.Context) error {
	return c.Ping(ctx)
}

// Close1E 包装 mcClient.Close
func (c *mcClient) Close1E() error {
	return c.Close()
}

// ================= 高层寄存器读写封装 =================

// 设备码映射，按需增补
var devCode = map[string]byte{
	"D": 0x44,
	"M": 0x4D,
}

// ---------- 帧构造 ----------
func buildReadFrame(code byte, addr uint16, length uint16) []byte {
	return []byte{
		0x01, 0xFF, 0x0A, 0x00, // 读命令/PLC号/定时器
		byte(addr & 0xFF), byte(addr >> 8), 0x00, 0x00, // 起始地址 (4byte)
		0x20, code, // 设备标识 & 设备码
		byte(length & 0xFF), byte(length >> 8), // 点数
	}
}

func buildWriteFrame(code byte, addr uint16, vals []uint16) []byte {
	length := uint16(len(vals))
	hdr := []byte{
		0x03, 0xFF, 0x0A, 0x00,
		byte(addr & 0xFF), byte(addr >> 8), 0x00, 0x00,
		0x20, code,
		byte(length & 0xFF), byte(length >> 8),
	}
	data := make([]byte, 0, len(vals)*2)
	for _, v := range vals {
		data = append(data, byte(v&0xFF), byte(v>>8))
	}
	return append(hdr, data...)
}
