package utils

import (
	"fmt"
	"log"
	"net"
)

// GetLocalIPs returns a list of non-loopback IPv4 addresses for the host.
func GetLocalIPs() ([]string, error) {
	var ips []string
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("无法获取网络接口: %w", err)
	}

	for _, i := range interfaces {
		// Interface is down or loopback, skip
		if i.Flags&net.FlagUp == 0 || i.Flags&net.FlagLoopback != 0 {
			continue
		}
		addrs, err := i.Addrs()
		if err != nil {
			// Log as a warning, as other interfaces might be fine
			log.Printf("[logger] utils.GetLocalIPs: 无法获取接口 %s 的地址: %v", i.Name, err)
			continue
		}

		for _, addr := range addrs {
			var ip net.IP
			switch v := addr.(type) {
			case *net.IPNet:
				ip = v.IP
			case *net.IPAddr:
				ip = v.IP
			}
			// Consider only IPv4 addresses that are not loopback
			if ip != nil && ip.To4() != nil && !ip.IsLoopback() {
				ips = append(ips, ip.String())
			}
		}
	}
	if len(ips) == 0 {
		return nil, fmt.Errorf("未找到有效的非环回 IPv4 地址")
	}
	return ips, nil
}

// CalculateCumulativeAndHandleReset 计算新的日累计值，并处理PLC计数器复位。
// 这是一个公共函数，可被各种协议的 reader 调用。
func CalculateCumulativeAndHandleReset(
	lineCode string, // 用于日志记录
	valueName string, // 用于日志记录，指明是 "Input" 还是 "Output"
	currentDailyCumulative int, // 当前应用层已累计的当日总量
	appLastKnownPLCValue int, // 应用层记录的、上次用于计算增量的PLC原始值
	currentPLCValue int, // 本次从PLC新读取到的原始值
) (newDailyCumulative int, newAppLastKnownPLCValueForNextCycle int) {

	if appLastKnownPLCValue == -1 {
		// 情况1：午夜重置后，或程序/Reader实例启动后的首次成功PLC读取。
		newDailyCumulative = currentPLCValue
		newAppLastKnownPLCValueForNextCycle = currentPLCValue
		log.Printf("[PlcPoclReader-%s] %s: 首次读取或午夜重置后。PLC原始值: %d。以此作为今日累计起点，并更新上次PLC原始值记录。",
			lineCode, valueName, currentPLCValue)
	} else if currentPLCValue < appLastKnownPLCValue {
		// 情况2：本次PLC的原始读数 < 应用层记录的上次PLC原始读数。
		// 这通常强烈暗示PLC的计数器发生了复位。
		log.Printf("[PlcPoclReader-%s] %s: 检测到PLC计数器可能已复位。上次PLC值: %d, 本次PLC值: %d。将本次PLC值 %d 视为复位后的新增量, 加入到之前的日累计 %d 中。",
			lineCode, valueName, appLastKnownPLCValue, currentPLCValue, currentPLCValue, currentDailyCumulative)
		newDailyCumulative = currentDailyCumulative + currentPLCValue
		newAppLastKnownPLCValueForNextCycle = currentPLCValue
	} else {
		// 情况3：currentPLCValue >= appLastKnownPLCValue (PLC计数器正常增加，或保持不变)
		increment := currentPLCValue - appLastKnownPLCValue
		newDailyCumulative = currentDailyCumulative + increment
		newAppLastKnownPLCValueForNextCycle = currentPLCValue
		if increment > 0 {
			log.Printf("[PlcPoclReader-%s] %s: PLC计数器正常增加。上次PLC值: %d, 本次PLC值: %d。增量: %d。旧日累计: %d。新日累计: %d。",
				lineCode, valueName, appLastKnownPLCValue, currentPLCValue, increment, currentDailyCumulative, newDailyCumulative)
		}
	}
	return newDailyCumulative, newAppLastKnownPLCValueForNextCycle
}

// CalculateCumulativeWithBaseline 基于基准值计算累计值，支持0点后从0开始累计
// 这个函数替代原有的CalculateCumulativeAndHandleReset，提供更精确的累计逻辑
func CalculateCumulativeWithBaseline(
	lineCode string,
	valueName string,
	currentDailyCumulative int,
	appLastKnownPLCValue int,
	currentPLCValue int,
	dailyBaseline *int, // 当天基准值的指针，允许修改
	resetCompensation *int, // 重置补偿值的指针，允许修改
) (newDailyCumulative int, newAppLastKnownPLCValue int) {

	if appLastKnownPLCValue == -1 {
		// 情况1：午夜重置后或程序启动后的首次读取
		if *dailyBaseline == -1 {
			// 如果还没有设置基准值，将当前PLC值作为基准值
			*dailyBaseline = currentPLCValue
			*resetCompensation = 0
			log.Printf("[%s] %s: 设置新的日基准值: %d", lineCode, valueName, *dailyBaseline)
		} else {
			// 检测PLC是否重启：如果当前PLC值远小于基准值，可能是PLC重启了
			// 使用80%作为阈值来判断是否为PLC重启
			if currentPLCValue < *dailyBaseline && (*dailyBaseline-currentPLCValue) > (*dailyBaseline*4/5) {
				log.Printf("[%s] %s: 检测到PLC可能重启。原基准值:%d, 当前PLC:%d, 重新设置基准值",
					lineCode, valueName, *dailyBaseline, currentPLCValue)
				*dailyBaseline = currentPLCValue // 重新设置基准值
				*resetCompensation = 0           // 清零补偿值
			}
		}

		// 新一天累计从0开始（无论PLC值是多少）
		newDailyCumulative = 0
		newAppLastKnownPLCValue = currentPLCValue

		log.Printf("[%s] %s: 新一天首次读取。基准值:%d, 当前PLC:%d, 补偿:%d, 累计值从0开始",
			lineCode, valueName, *dailyBaseline, currentPLCValue, *resetCompensation)

	} else if currentPLCValue < appLastKnownPLCValue {
		// 情况2：检测到PLC重置
		log.Printf("[%s] %s: 检测到PLC重置。上次:%d, 当前:%d",
			lineCode, valueName, appLastKnownPLCValue, currentPLCValue)

		// 将重置前的值累加到补偿中
		resetBeforeValue := appLastKnownPLCValue - *dailyBaseline
		if resetBeforeValue > 0 {
			*resetCompensation = *resetCompensation + resetBeforeValue
		}

		// 更新基准值为重置后的值
		*dailyBaseline = currentPLCValue

		// 重新计算累计值
		newDailyCumulative = *resetCompensation
		newAppLastKnownPLCValue = currentPLCValue

		log.Printf("[%s] %s: PLC重置处理完成。新基准:%d, 新补偿:%d, 新累计:%d",
			lineCode, valueName, *dailyBaseline, *resetCompensation, newDailyCumulative)

	} else {
		// 情况3：正常增长
		newDailyCumulative = (currentPLCValue - *dailyBaseline) + *resetCompensation
		if newDailyCumulative < currentDailyCumulative {
			// 防止累计值倒退
			newDailyCumulative = currentDailyCumulative
		}

		newAppLastKnownPLCValue = currentPLCValue

		if newDailyCumulative != currentDailyCumulative {
			log.Printf("[%s] %s: 正常增长。基准:%d, 当前PLC:%d, 补偿:%d, 累计值:%d->%d",
				lineCode, valueName, *dailyBaseline, currentPLCValue, *resetCompensation,
				currentDailyCumulative, newDailyCumulative)
		}
	}

	return newDailyCumulative, newAppLastKnownPLCValue
}
