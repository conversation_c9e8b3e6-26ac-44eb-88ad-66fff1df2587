package datastore

import (
	"context" // Needed for StartListeners
	"sync"    // Needed for StartListeners
	"time"    // Placeholder, might be needed for DTOs if timestamps are handled

	"modbus-app/backend/v1/messages" // Assuming messages are needed by Datastore interface
)

// ManagedLineInfo 定义了从数据库获取的生产线基本信息
// 这些信息将与 config.PollingRuntimeConfig 结合使用
type ManagedLineInfo struct {
	ProductionLineID int    // 对应 U_MesLinesController_T.id
	ServerIP         string // 对应 U_MesLinesController_T.server_ip (用于确认归属)
	LineCode         string // 对应 U_MesLinesController_T.line_code
	LineName         string // 对应 U_MesLinesController_T.line_name

	// 新增和调整的字段，以支持多种协议和动态地址
	PlcProtocol       string `json:"plc_protocol"`        // 协议类型 (e.g., "mbp", "mcp1e")
	PlcIPAddress      string `json:"plc_ip_address"`      // PLC 的 IP 地址
	PlcPort           int    `json:"plc_port"`            // PLC 的端口
	PlcSlaveID        int    `json:"plc_slave_id"`        // 从站ID/单元号
	PlcTimeoutSec     int    `json:"plc_timeout_sec"`     // 超时时间（秒）
	InputAddress      string `json:"input_address"`       // 投入计数地址 (e.g., "40001", "D100")
	InputTimeAddress  string `json:"input_time_address"`  // 投入节拍地址
	OutputAddress     string `json:"output_address"`      // 产出计数地址
	OutputTimeAddress string `json:"output_time_address"` // 产出节拍地址

	CreatedDatetime time.Time
	UpdatedDatetime time.Time
}

// Datastore 是数据存储操作的接口定义
type Datastore interface {
	Connect() error
	Close() error

	// UpsertRealTimeData 处理实时数据的写入请求
	// RealtimeDataMessage.RealDatetime 是关键的事件时间戳
	// Datastore 内部逻辑决定数据的业务归属日期 (att_date)
	UpsertRealTimeData(msg messages.RealtimeDataMessage) error

	// LogErrorToDB 将错误日志写入数据库
	LogErrorToDB(logMsg messages.LogErrorMessage) error

	// GetCurrentBusinessDate 获取当前的业务处理日期 (如果Datastore需要管理这个概念)
	// 这个日期的更新可能由Scheduler的午夜任务触发
	// GetCurrentBusinessDate() (time.Time, error) // 暂时注释，其必要性待定

	// HandleMidnightReset 处理午夜重置任务，可能涉及业务日期的更新
	// HandleMidnightReset(msg messages.MidnightResetMessage) error // 暂时注释

	// GetManagedLinesByServerIPs 根据服务器IP列表从数据库获取此实例应管理的生产线信息
	GetManagedLinesByServerIPs(serverIPs []string) ([]ManagedLineInfo, error)

	// FetchLineControllerConfig(lineCode string) (*config.LineControllerConfig, error) // 这个方法可能被 GetManagedLinesByServerIPs 替代或调整
	// FetchPollingRuntimeConfig(lineCode string) (*config.PollingRuntimeConfig, error) // 这个应该由config模块处理

	// StartListeners 启动后台 goroutines 来监听输入 channels
	StartListeners(
		ctx context.Context, // Application context for graceful shutdown
		wg *sync.WaitGroup, // WaitGroup to signal listener completion
		errorLogChan <-chan messages.LogErrorMessage, // Channel for receiving error logs
		dataWriteChan <-chan messages.RealtimeDataMessage, // Channel for receiving real-time data
		// midnightTaskChan <-chan messages.MidnightResetMessage, // Placeholder for future use
	)

	GetLatestState(lineInfo ManagedLineInfo, forDate time.Time) (latestCumulativeInput int, latestPlcInput int, latestCumulativeOutput int, latestPlcOutput int, err error)

	// GetLatestStateWithBaseline 获取包含基准值和补偿值的完整状态信息
	GetLatestStateWithBaseline(lineInfo ManagedLineInfo, forDate time.Time) (latestCumulativeInput int, latestPlcInput int, latestCumulativeOutput int, latestPlcOutput int, baselineInput int, baselineOutput int, compensationInput int, compensationOutput int, err error)
}

// NewDatastoreManager 创建一个新的 DatastoreManager 实例 (示例函数名)
// 具体的实现将在 sqlstore 子包中
// func NewDatastoreManager(cfg config.DatabaseConfig, errorLogChannel chan messages.LogErrorMessage) Datastore {
//    // return sqlstore.NewSQLStore(cfg, errorLogChannel)
//    return nil // Placeholder
// }

// 注意:
// 1. `REPLACE_WITH_ACTUAL_MODULE_PATH` 需要替换为实际的项目模块路径。
// 2. `config.LineControllerConfig` 和 `config.PollingRuntimeConfig` 的获取逻辑可能需要调整。
//    `LineControllerConfig` 的静态部分现在可能来自 `ManagedLineInfo`。
//    `PollingRuntimeConfig` 仍从主配置加载，通过 LineCode 关联。
