-- 创建支持基准值和补偿值的本机编译存储过程
-- 这个存储过程扩展了原有的sp_UpsertRealTimeData_New，增加了基准值和补偿值字段

CREATE PROCEDURE [dbo].[sp_UpsertRealTimeData_WithBaseline]
    -- 定义输入参数，数据类型应与表列精确匹配
    @att_date DATE,
    @line_code VARCHAR(50), -- 假设 line_code 是 VARCHAR(50)
    @task_num VARCHAR(50), -- 假设 task_num 是 VARCHAR(50)
    @reg_input INT,
    @reg_input_time INT,
    @reg_output INT,
    @reg_output_time INT,
    @updated_datetime datetime,
    @last_plc_input INT,
    @last_plc_output INT,
    @daily_baseline_input INT,
    @daily_baseline_output INT,
    @reset_compensation_input INT,
    @reset_compensation_output INT
-- WITH NATIVE_COMPILATION: 声明为本机编译
-- SCHEMABINDING: 必须，将过程绑定到其引用的对象架构
-- EXECUTE AS OWNER: 指定执行上下文，OWNER 通常更简单
WITH NATIVE_COMPILATION, SCHEMABINDING --, EXECUTE AS OWNER -- 根据需要选择 EXECUTE AS
AS
BEGIN ATOMIC WITH (
    -- TRANSACTION ISOLATION LEVEL: 内存优化表常用 SNAPSHOT
    -- LANGUAGE: 必须指定
    TRANSACTION ISOLATION LEVEL = SNAPSHOT,
    LANGUAGE = N'us_english' -- 或者 N'简体中文' 等
)

    DECLARE @FLAG_PUTQTY VARCHAR(10)
    DECLARE @OUTPUT INT
    DECLARE @OUTPUT_TIME INT
--  SELECT @FLAG_PUTQTY=Z_TRCC FROM MAMac WHERE MacID=@line_code;

    DECLARE @INPUT INT
    DECLARE @INPUT_TIME INT

    DECLARE @LAST_INPUT INT
    DECLARE @LAST_OUTPUT INT

    -- 新增：基准值和补偿值变量
    DECLARE @BASELINE_INPUT INT
    DECLARE @BASELINE_OUTPUT INT
    DECLARE @COMPENSATION_INPUT INT
    DECLARE @COMPENSATION_OUTPUT INT

    IF ISNULL(@reg_input,-1) >= 0
    BEGIN
        SET @INPUT = @reg_input
        SET @INPUT_TIME = @reg_input_time
    END

    IF ISNULL(@reg_output,-1) >= 0
    BEGIN
        SET @OUTPUT = @reg_output
        SET @OUTPUT_TIME = @reg_output_time
    END

    IF @line_code IN ('2001')
    BEGIN
        SET @FLAG_PUTQTY = 'Y'
    END

    IF ISNULL(@FLAG_PUTQTY,'N') = 'Y'
    BEGIN
       SET @OUTPUT = @INPUT
       SET @OUTPUT_TIME = @INPUT_TIME
    END

    IF ISNULL(@last_plc_input,-1) >= 0
    BEGIN
        SET @LAST_INPUT = @last_plc_input
    END

    IF ISNULL(@last_plc_output,-1) >= 0
    BEGIN
        SET @LAST_OUTPUT = @last_plc_output
    END

    -- 新增：处理基准值和补偿值
    IF ISNULL(@daily_baseline_input,-1) >= 0
    BEGIN
        SET @BASELINE_INPUT = @daily_baseline_input
    END

    IF ISNULL(@daily_baseline_output,-1) >= 0
    BEGIN
        SET @BASELINE_OUTPUT = @daily_baseline_output
    END

    IF ISNULL(@reset_compensation_input,-1) >= 0
    BEGIN
        SET @COMPENSATION_INPUT = @reset_compensation_input
    END

    IF ISNULL(@reset_compensation_output,-1) >= 0
    BEGIN
        SET @COMPENSATION_OUTPUT = @reset_compensation_output
    END

    -- 1. 尝试更新现有行
    UPDATE dbo.U_MesLinesRealTimeData
    SET
        updated_datetime = @updated_datetime,
        reg_input = ISNULL(@INPUT,reg_input),
        reg_input_time = ISNULL(@INPUT_TIME,reg_input_time),
        reg_output = ISNULL(@OUTPUT,reg_output),
        reg_output_time = ISNULL(@OUTPUT_TIME,reg_output_time),
        last_plc_input   = ISNULL(@LAST_INPUT, last_plc_input),
        last_plc_output  = ISNULL(@LAST_OUTPUT, last_plc_output),
        daily_baseline_input = ISNULL(@BASELINE_INPUT, daily_baseline_input),
        daily_baseline_output = ISNULL(@BASELINE_OUTPUT, daily_baseline_output),
        reset_compensation_input = ISNULL(@COMPENSATION_INPUT, reset_compensation_input),
        reset_compensation_output = ISNULL(@COMPENSATION_OUTPUT, reset_compensation_output)
    WHERE att_date = @att_date AND line_code = @line_code;

    -- 2. 如果没有行被更新 (@@ROWCOUNT = 0)，则插入新行
    IF @@ROWCOUNT = 0
    BEGIN
        INSERT INTO dbo.U_MesLinesRealTimeData (
            att_date,
            line_code,
            task_num,
            reg_input,
            reg_input_time,
            reg_output,
            reg_output_time,
            last_plc_input,
            last_plc_output,
            daily_baseline_input,
            daily_baseline_output,
            reset_compensation_input,
            reset_compensation_output
        )
        VALUES (
            @att_date,
            @line_code,
            @task_num,
            ISNULL(@INPUT,0),
            ISNULL(@INPUT_TIME,0),
            ISNULL(@OUTPUT,0),
            ISNULL(@OUTPUT_TIME,0),
            @LAST_INPUT,
            @LAST_OUTPUT,
            ISNULL(@BASELINE_INPUT,-1),
            ISNULL(@BASELINE_OUTPUT,-1),
            ISNULL(@COMPENSATION_INPUT,0),
            ISNULL(@COMPENSATION_OUTPUT,0)
        );
    END

    -- 3. 更新小时数据表
    UPDATE dbo.U_MesLinesRealTimeDataLine
    SET
        updated_datetime = @updated_datetime,
        hour_input    = ISNULL(@INPUT,hour_input),
        hour_output    = ISNULL(@OUTPUT,hour_output)
    WHERE att_date = @att_date AND line_code = @line_code AND hour_qty = datepart(hour,@updated_datetime);

    IF @@ROWCOUNT = 0
    BEGIN
        INSERT INTO dbo.U_MesLinesRealTimeDataLine (
            att_date,
            line_code,
            hour_qty,
            hour_input,
            hour_output
        )
        VALUES (
            @att_date,
            @line_code,
            datepart(hour,@updated_datetime),
            ISNULL(@INPUT,0),
            ISNULL(@OUTPUT,0)
        );
    END

    -- 4. 调用任务更新存储过程
    IF ISNULL(@INPUT,-1) >= 0
    BEGIN
        EXEC [dbo].[sp_UpdateRealTimeDataTask] @att_date, @line_code, @INPUT, @INPUT_TIME, @updated_datetime, 1;
    END

    IF ISNULL(@OUTPUT,-1) >= 0
    BEGIN
        EXEC [dbo].[sp_UpdateRealTimeDataTask] @att_date, @line_code, @OUTPUT, @OUTPUT_TIME, @updated_datetime, 0;
    END

END
