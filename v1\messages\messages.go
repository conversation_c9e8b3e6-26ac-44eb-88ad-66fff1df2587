package messages

import "time"

// TaskType 定义了任务的类型
type TaskType string

// 定义具体的任务类型常量
const (
	TaskMidnightReset   TaskType = "MIDNIGHT_RESET"
	TaskCustomScheduled TaskType = "CUSTOM_SCHEDULED_TASK"
	TaskRealtimeData    TaskType = "REALTIME_DATA"
	TaskLogEvent        TaskType = "LOG_EVENT"         // 用于调度器等模块发送普通事件或错误日志
	TaskHourlyForceSend TaskType = "HOURLY_FORCE_SEND" // New: 用于每小时强制发送任务
	// 可以根据需要添加其他任务类型，例如 CONFIG_REQUEST, CONFIG_RESPONSE
)

// SeverityType 定义了日志消息的级别
type SeverityType string

// 定义具体的日志级别常量
const (
	SeverityInfo    SeverityType = "INFO"
	SeverityWarning SeverityType = "WARNING"
	SeverityError   SeverityType = "ERROR"
	SeverityDebug   SeverityType = "DEBUG"
)

// BaseMessage 是所有消息的基础结构
type BaseMessage struct {
	Type      TaskType  // 消息的具体类型
	Timestamp time.Time // 消息生成的时间戳
}

// MidnightResetMessage 调度器发送给数据存储模块，用于通知午夜并可能触发日期相关的重置
// (当前被告知暂时不考虑0点功能，但保留结构以备将来使用)
type MidnightResetMessage struct {
	BaseMessage
}

// CustomScheduledTaskMessage 调度器发送，用于执行一个自定义的计划任务
type CustomScheduledTaskMessage struct {
	BaseMessage
	TaskID  string      // 计划任务的唯一标识
	Payload interface{} // 任务的具体负载，由消费者解析
}

// RealtimeDataMessage Modbus读取器发送给数据存储模块，包含读取到的实时数据
type RealtimeDataMessage struct {
	BaseMessage
	LineCode     string    `json:"lineCode"`
	RealDatetime time.Time `json:"realDatetime"`
	Input        int       `json:"input"`
	Output       int       `json:"output"`
	InputTime    int       `json:"inputTime"`
	OutputTime   int       `json:"outputTime"`
	// 新增: 附带的上次用于计算增量的PLC原始值，用于持久化以支持重启恢复
	AppLastKnownPLCInput  int `json:"appLastKnownPLCInput"`
	AppLastKnownPLCOutput int `json:"appLastKnownPLCOutput"`
	// 新增: 日基准值和重置补偿值，用于支持0点后从0开始累计
	DailyBaselineInput      int `json:"dailyBaselineInput"`
	DailyBaselineOutput     int `json:"dailyBaselineOutput"`
	ResetCompensationInput  int `json:"resetCompensationInput"`
	ResetCompensationOutput int `json:"resetCompensationOutput"`
	// 根据实际 PLC 数据点可以增加更多字段，或者使用一个 map[string]interface{} 类型的 Data 字段
	// Example: Data map[string]interface{} `json:"data"`
}

// LogErrorMessage 任何模块都可能发送给数据存储模块，用于记录错误或重要事件
type LogErrorMessage struct {
	BaseMessage
	Source   string                 // 日志来源模块 (例如 "Scheduler", "ModbusReader-Line3011", "DatastoreManager")
	Severity SeverityType           // 日志级别 (INFO, WARNING, ERROR, DEBUG)
	Message  string                 // 日志消息文本
	Details  map[string]interface{} // 可选的结构化错误详情或上下文信息 (例如，错误堆栈，相关参数)
}

// ForceSendLastKnownDataMessage 是一个命令，指示ModbusReader发送其最新的已知（已发送到数据存储）数据
// 由调度器在每小时的特定时间点触发，通过 main.go 中的广播机制发送给所有 ModbusReader。
type ForceSendLastKnownDataMessage struct {
	BaseMessage
}

// NewForceSendLastKnownDataMessage 创建一个新的 ForceSendLastKnownDataMessage.
// 调度器 (scheduler.go) 在创建此消息时，应确保 BaseMessage.Type 设置为 TaskHourlyForceSend。
func NewForceSendLastKnownDataMessage() ForceSendLastKnownDataMessage {
	return ForceSendLastKnownDataMessage{
		BaseMessage: BaseMessage{
			Type:      TaskHourlyForceSend, // 使用 TaskHourlyForceSend 常量
			Timestamp: time.Now(),
		},
	}
}
