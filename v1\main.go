package main

import (
	"context"
	"database/sql" // For *sql.DB type

	// For error formatting
	"log" // For utils.GetLocalIPs if it were in main, or if utils needs it passed
	"os/signal"
	"sync"
	"syscall"
	"time" // For retry delays

	"modbus-app/backend/v1/config"
	"modbus-app/backend/v1/datastore"          // Datastore interface
	"modbus-app/backend/v1/datastore/sqlstore" // SQLStore implementation
	"modbus-app/backend/v1/mcreader"
	"modbus-app/backend/v1/messages"
	"modbus-app/backend/v1/modbusreader"
	"modbus-app/backend/v1/scheduler" // Added Scheduler import
	"modbus-app/backend/v1/utils"     // Now actively used

	// Database driver
	_ "github.com/denisenkom/go-mssqldb"
)

// getLocalIPs function has been moved to v1/utils/utils.go

const (
	// Default channel names for scheduler tasks, can be overridden by config
	// Example: defaultMidnightTaskChannel = "midnightTasks"
	midnightNotificationChannelName = "midnight_notifications"          // For broadcasting reset to ModbusReaders
	hourlyForceSendChannelName      = "hourly_force_send_notifications" // New: For broadcasting force send commands
)

func main() {
	// log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.SetFlags(log.LstdFlags)
	log.Println("[Main]: 应用程序启动中...")

	// Load application configuration
	cfg, err := config.LoadConfig("v1/config/config.json") // Adjusted path for v1
	if err != nil {
		log.Fatalf("[Main]: 无法加载配置 [config.json] 文件: %v", err)
	}
	log.Println("[Main]: 配置 [config.json] 文件加载成功。")

	// Setup context for graceful shutdown
	appCtx, cancel := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer cancel()

	var wg sync.WaitGroup

	// Initialize Datastore module with infinite retry
	var db *sql.DB
	var ds datastore.Datastore
	dbRetryDelay := 1 * time.Minute // Changed from 5 minutes for faster startup in dev, can be configured
	log.Println("[Main]: 开始无限次尝试初始化数据库连接...")
	for i := 0; ; i++ {
		if i > 0 {
			log.Printf("[Main]: 等待 %v 后重试数据库连接...", dbRetryDelay)
			select {
			case <-time.After(dbRetryDelay):
			case <-appCtx.Done():
				log.Println("[Main]: 数据库重试期间收到关闭信号，退出。")
				return
			}
		}
		log.Printf("[Main]: 尝试初始化数据库连接... (第 %d 次)", i+1)
		db, err = sqlstore.InitializeDatabase(cfg.Database)
		if err == nil {
			ds = sqlstore.NewSQLStore(db, cfg.Database)
			log.Println("[Main]: 数据库连接成功！")
			break
		}
		log.Printf("[Main]: 数据库连接失败 (第 %d 次): %v", i+1, err)
		if appCtx.Err() != nil { // Check for shutdown signal immediately after failure
			log.Println("[Main]: 数据库连接失败后检测到关闭信号，退出。")
			if db != nil {
				db.Close()
			}
			return
		}
	}
	defer func() {
		if ds != nil {
			log.Println("[Main]: 正在关闭数据存储模块...")
			if err := ds.Close(); err != nil {
				log.Printf("[Main]: 关闭数据存储模块失败: %v", err)
			}
		} else if db != nil {
			log.Println("[Main]: 数据存储未完全初始化，但尝试关闭原始数据库连接...")
			db.Close()
		}
	}()
	log.Println("[Main]: 数据存储模块初始化成功。")

	// Get local IPs with infinite retry
	var localIPs []string
	getIPRetryDelay := 1 * time.Minute
	log.Println("[Main]: 开始无限次尝试获取本地IP地址...")
	for i := 0; ; i++ {
		if i > 0 {
			log.Printf("[Main]: 等待 %v 后重试获取本地IP...", getIPRetryDelay)
			select {
			case <-time.After(getIPRetryDelay):
			case <-appCtx.Done():
				log.Println("[Main]: 获取IP重试期间收到关闭信号，退出。")
				return
			}
		}
		log.Printf("[Main]: 尝试获取本地IP地址... (第 %d 次尝试)", i+1)

		retryNeeded := false // Flag to indicate if a retry is needed for this iteration

		localIPs, err = utils.GetLocalIPs()
		if err != nil {
			log.Printf("[Main]: 获取本地IP地址失败 (第 %d 次尝试): %v", i+1, err)
			retryNeeded = true
		} else { // err == nil
			if len(localIPs) > 0 {
				log.Printf("[Main]: 成功获取到本地IP地址: %v", localIPs)
				break // Success: no error and got at least one IP
			} else {
				// err == nil AND len(localIPs) == 0
				log.Printf("[Main]: 获取本地IP地址调用成功，但未返回任何IP地址。预期应有IP数据，将继续尝试... (第 %d 次尝试)", i+1)
				retryNeeded = true // Treat as needing a retry
			}
		}

		// If a retry is needed (due to error or zero IPs) and app is shutting down, then exit.
		if retryNeeded && appCtx.Err() != nil {
			log.Println("[Main]: 在尝试获取IP地址（因错误或未获取到预期数据）后检测到关闭信号，退出。")
			return
		}
		// If retryNeeded is false, we broke the loop.
		// If retryNeeded is true and appCtx.Err() is nil, the loop continues to the next iteration.
	}

	// Fetch managed lines from datastore using localIPs with infinite retry
	var managedLines []datastore.ManagedLineInfo
	getLinesRetryDelay := 1 * time.Minute
	log.Println("[Main]: 开始无限次尝试从数据存储获取管理的生产线信息...")
	for i := 0; ; i++ {
		if i > 0 {
			log.Printf("[Main]: 等待 %v 后重试获取生产线信息...", getLinesRetryDelay)
			select {
			case <-time.After(getLinesRetryDelay):
			case <-appCtx.Done():
				log.Println("[Main]: 获取生产线信息重试期间收到关闭信号，退出。")
				return
			}
		}
		log.Printf("[Main]: 尝试从数据存储获取管理的生产线信息... (第 %d 次尝试)", i+1)

		retryNeeded := false // Flag to indicate if a retry is needed for this iteration

		managedLines, err = ds.GetManagedLinesByServerIPs(localIPs)
		if err != nil {
			log.Printf("[Main]: 从数据存储获取管理的生产线信息失败 (第 %d 次尝试): %v", i+1, err)
			retryNeeded = true
		} else { // err == nil
			if len(managedLines) > 0 {
				log.Printf("[Main]: 成功从数据存储获取到 %d 条管理的生产线。", len(managedLines))
				break // Success: no error and got at least one line
			} else {
				// err == nil AND len(managedLines) == 0
				log.Printf("[Main]: 数据存储查询成功，但未返回任何管理的生产线信息 (基于查询IPs: %v)。预期应有生产线数据，将继续尝试... (第 %d 次尝试)", localIPs, i+1)
				retryNeeded = true // Treat as needing a retry
			}
		}

		// If a retry is needed (due to error or zero lines) and app is shutting down, then exit.
		if retryNeeded && appCtx.Err() != nil {
			log.Println("[Main]: 在尝试获取生产线信息（因错误或未获取到预期数据）后检测到关闭信号，退出。")
			return
		}
		// If retryNeeded is false, we broke the loop.
		// If retryNeeded is true and appCtx.Err() is nil, the loop continues to the next iteration.
	}

	// Initialize communication channels
	errorLogChannel := make(chan messages.LogErrorMessage, 100)
	dataWriteChannel := make(chan messages.RealtimeDataMessage, 1000)
	log.Println("[Main]: 通信 channels 初始化完成。")

	// Initialize Datastore workers/listeners
	// ds is the initialized datastore.Datastore instance
	// wg is the main WaitGroup for all goroutines
	// SQLStore.StartListeners内部会启动2个goroutine，所以需要预先Add(2)
	wg.Add(2)                                                                    // 为SQLStore的2个listener goroutines预留计数
	ds.StartListeners(appCtx, &wg, errorLogChannel, dataWriteChannel /*, nil */) // Pass nil for midnightTaskChan for now
	log.Println("[Main]: 数据更新 listeners 已安排启动。")

	// Initialize Scheduler module
	log.Println("[Main]: 准备初始化调度器模块...")
	taskChannels := make(map[string]chan<- interface{})

	// Create the dedicated channel for midnight notifications to ModbusReaders
	midnightNotificationChan := make(chan interface{}, 10) // Buffer for the single midnight message
	taskChannels[midnightNotificationChannelName] = midnightNotificationChan
	log.Printf("[Main]: 为午夜重置通知创建了 channel: %s", midnightNotificationChannelName)

	// Create the dedicated channel for hourly force send notifications to ModbusReaders
	hourlyForceSendChan := make(chan interface{}, 10) // Buffer for hourly messages
	taskChannels[hourlyForceSendChannelName] = hourlyForceSendChan
	log.Printf("[Main]: 为每小时强制发送通知创建了 channel: %s", hourlyForceSendChannelName)

	// Dynamically create channels for tasks defined in config
	for _, taskCfg := range cfg.Scheduler.Tasks {
		if taskCfg.TargetChannel != "" {
			if _, exists := taskChannels[taskCfg.TargetChannel]; !exists {
				log.Printf("[Main]: 为调度器任务目标 '%s' 创建新的 channel", taskCfg.TargetChannel)
				taskChannels[taskCfg.TargetChannel] = make(chan interface{}, 20) // Buffer size 20, can be configured
			}
		}
	}
	log.Printf("[Main]: 为调度器任务创建了 %d 个目标 channels。", len(taskChannels))

	sched, err := scheduler.NewScheduler(cfg.Scheduler, errorLogChannel, taskChannels)
	if err != nil {
		log.Fatalf("[Main]: 无法初始化调度器模块: %v", err)
	}
	log.Println("[Main]: 调度器模块初始化成功。")
	wg.Add(1)
	go sched.Start(appCtx, &wg)
	log.Println("[Main]: 调度器模块已安排启动。")

	// --- Reader 初始化和启动 ---
	// 为所有 Reader 创建一个共享的命令 channel，用于午夜重置和强制发送等任务
	allReadersCommandChannel := make(chan interface{}, 100)
	// 将这个 channel 添加到任务 channel 映射中，以便调度器可以向它发送消息
	taskChannels[midnightNotificationChannelName] = allReadersCommandChannel
	taskChannels[hourlyForceSendChannelName] = allReadersCommandChannel
	log.Println("[Main]: 为所有 Reader 创建了共享的命令 channel，并与调度器关联。")

	log.Printf("[Main]: 准备为 %d 条管理的生产线启动读取器...", len(managedLines))
	for _, line := range managedLines {
		currentLine := line // Capture range variable

		log.Printf("[Main]: 处理生产线: %s (%s), 协议: %s", currentLine.LineName, currentLine.LineCode, currentLine.PlcProtocol)

		var pollingCfg *config.PollingRuntimeConfig
		// 在循环开始时先找到默认配置
		var defaultConfig *config.PollingRuntimeConfig
		for i := range cfg.PollingRuntimes {
			if cfg.PollingRuntimes[i].LineCode == "DEFAULT" {
				defaultConfig = &cfg.PollingRuntimes[i]
				break
			}
		}

		// 尝试为当前产线找到特定配置
		for i := range cfg.PollingRuntimes {
			if cfg.PollingRuntimes[i].LineCode == currentLine.LineCode {
				pollingCfg = &cfg.PollingRuntimes[i]
				break
			}
		}

		// 如果没有找到特定配置，则使用默认配置
		if pollingCfg == nil {
			pollingCfg = defaultConfig
		}

		if pollingCfg == nil {
			log.Printf("[Main] 产线 %s: 无法找到特定或默认的轮询配置，跳过此产线。", currentLine.LineCode)
			continue
		}

		// --- 决定最终的超时时间 ---
		// 默认使用 config.json 中的配置
		responseTimeout := time.Duration(pollingCfg.ResponseTimeoutMs) * time.Millisecond
		// 如果数据库中配置了有效的超时时间（大于0），则优先使用数据库中的配置
		if currentLine.PlcTimeoutSec > 0 {
			dbTimeout := time.Duration(currentLine.PlcTimeoutSec) * time.Second
			log.Printf("[Main] 产线 %s: 数据库中配置了超时时间 (%v)，将覆盖 config.json 中的默认值 (%v)。",
				currentLine.LineCode, dbTimeout, responseTimeout)
			responseTimeout = dbTimeout
		}
		// --- 结束超时时间决定逻辑 ---

		// --- 新的持久化状态恢复逻辑（支持基准值和补偿值）---
		log.Printf("[Main] 正在为产线 %s 获取最新的持久化状态（包含基准值）...", currentLine.LineCode)
		cumulInput, plcInput, cumulOutput, plcOutput, baselineInput, baselineOutput, compensationInput, compensationOutput, err := ds.GetLatestStateWithBaseline(currentLine, time.Now())
		if err != nil {
			log.Printf("[Main] 无法为产线 %s 获取初始状态: %v。将从默认值开始。", currentLine.LineCode, err)
			cumulInput, plcInput, cumulOutput, plcOutput = -1, -1, -1, -1
			baselineInput, baselineOutput = -1, -1
			compensationInput, compensationOutput = 0, 0
		} else {
			log.Printf("[Main] 获取到产线 %s 的完整初始状态。CumulInput:%d, PlcInput:%d, CumulOutput:%d, PlcOutput:%d, BaselineInput:%d, BaselineOutput:%d, CompensationInput:%d, CompensationOutput:%d",
				currentLine.LineCode, cumulInput, plcInput, cumulOutput, plcOutput, baselineInput, baselineOutput, compensationInput, compensationOutput)
		}

		// 根据配置决定最终的初始值
		if currentLine.InputAddress == "" {
			cumulInput, plcInput = -1, -1
			baselineInput, compensationInput = -1, 0
		}
		if currentLine.OutputAddress == "" {
			cumulOutput, plcOutput = -1, -1
			baselineOutput, compensationOutput = -1, 0
		}

		// 如果配置了地址，但数据库没有历史记录(值为-1)，则将累计值设为0，PLC值保持-1 (首次启动)
		if currentLine.InputAddress != "" && cumulInput == -1 {
			cumulInput = 0
		}
		if currentLine.OutputAddress != "" && cumulOutput == -1 {
			cumulOutput = 0
		}

		// 基准值和补偿值的初始化处理
		if currentLine.InputAddress != "" && baselineInput == -1 {
			baselineInput = -1 // 等待第一次读取时设置
			compensationInput = 0
		}
		if currentLine.OutputAddress != "" && baselineOutput == -1 {
			baselineOutput = -1 // 等待第一次读取时设置
			compensationOutput = 0
		}

		log.Printf("[Main] 产线 %s 最终初始化状态: CumulInput:%d, PlcInput:%d, CumulOutput:%d, PlcOutput:%d, BaselineInput:%d, BaselineOutput:%d, CompensationInput:%d, CompensationOutput:%d",
			currentLine.LineCode, cumulInput, plcInput, cumulOutput, plcOutput, baselineInput, baselineOutput, compensationInput, compensationOutput)
		// --- 结束新的持久化状态恢复逻辑 ---

		switch currentLine.PlcProtocol {
		case "mbp": // Modbus Protocol
			readerConfig := modbusreader.CombinedReaderConfig{
				LineCode:                   currentLine.LineCode,
				ModbusIP:                   currentLine.PlcIPAddress,
				ModbusPort:                 currentLine.PlcPort,
				PlcSlaveID:                 int(currentLine.PlcSlaveID),
				ConnectTimeout:             time.Duration(pollingCfg.ConnectTimeoutMs) * time.Millisecond,
				ResponseTimeout:            responseTimeout,
				PollingInterval:            time.Duration(pollingCfg.PollingIntervalSec) * time.Second,
				FailureThreshold:           pollingCfg.FailureThreshold,
				BackoffIntervalsSec:        pollingCfg.BackoffIntervalsSec,
				FailuresToNextBackoffLevel: pollingCfg.FailuresToNextBackoffLevel,
				MaxConsecutiveFailures:     pollingCfg.MaxConsecutiveFailures,
				InputAddress:               currentLine.InputAddress,
				InputTimeAddress:           currentLine.InputTimeAddress,
				OutputAddress:              currentLine.OutputAddress,
				OutputTimeAddress:          currentLine.OutputTimeAddress,
			}

			mbReader, err := modbusreader.NewModbusReader(
				readerConfig,
				cumulInput, plcInput,
				cumulOutput, plcOutput,
				baselineInput, baselineOutput,
				compensationInput, compensationOutput,
				errorLogChannel,
				dataWriteChannel,
				appCtx.Done(),
				allReadersCommandChannel,
			)
			if err != nil {
				log.Printf("[Main] 无法为产线 %s 创建 Modbus 读取器: %v", currentLine.LineCode, err)
				continue
			}
			wg.Add(1)
			go mbReader.Start(&wg)
			log.Printf("[Main]: Modbus 读取器已为产线 %s 安排启动。", currentLine.LineCode)

		case "mcp1e": // MC Protocol 1E
			mcReaderConfig := mcreader.CombinedMcReaderConfig{
				LineCode:                   currentLine.LineCode,
				PlcIPAddress:               currentLine.PlcIPAddress,
				PlcPort:                    currentLine.PlcPort,
				ConnectTimeout:             time.Duration(pollingCfg.ConnectTimeoutMs) * time.Millisecond,
				ResponseTimeout:            responseTimeout,
				PollingInterval:            time.Duration(pollingCfg.PollingIntervalSec) * time.Second,
				FailureThreshold:           pollingCfg.FailureThreshold,
				BackoffIntervalsSec:        pollingCfg.BackoffIntervalsSec,
				FailuresToNextBackoffLevel: pollingCfg.FailuresToNextBackoffLevel,
				MaxConsecutiveFailures:     pollingCfg.MaxConsecutiveFailures,
				InputAddress:               currentLine.InputAddress,
				InputTimeAddress:           currentLine.InputTimeAddress,
				OutputAddress:              currentLine.OutputAddress,
				OutputTimeAddress:          currentLine.OutputTimeAddress,
			}

			mcReader, err := mcreader.NewMcReader(
				mcReaderConfig,
				cumulInput, plcInput,
				cumulOutput, plcOutput,
				baselineInput, baselineOutput,
				compensationInput, compensationOutput,
				errorLogChannel,
				dataWriteChannel,
				appCtx.Done(),
				allReadersCommandChannel,
			)
			if err != nil {
				log.Printf("[Main] 无法为产线 %s 创建 MC 读取器: %v", currentLine.LineCode, err)
				continue
			}
			wg.Add(1)
			go mcReader.Start(&wg)
			log.Printf("[Main]: MC 读取器已为产线 %s 安排启动。", currentLine.LineCode)

		default:
			log.Printf("[Main] 未知的产线协议 '%s'，跳过产线 %s。", currentLine.PlcProtocol, currentLine.LineCode)
		}
	}

	log.Println("[Main]: 所有读取器已安排启动。等待关闭信号...")

	// Wait for context to be cancelled (e.g., by SIGINT/SIGTERM)
	<-appCtx.Done()

	log.Println("[Main]: 收到关闭信号，等待所有 goroutines 完成...")
	wg.Wait()
	log.Println("[Main]: 所有 goroutines 已完成。应用程序正常退出。")
}
