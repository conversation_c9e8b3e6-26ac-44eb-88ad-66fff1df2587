package utils

import (
	"log"
	"time"

	"modbus-app/backend/v1/messages"
)

// Logger is a centralized component for sending structured log messages.
type Logger struct {
	errorLog<PERSON>han   chan<- messages.LogErrorMessage
	source         string
	defaultDetails map[string]interface{}
}

// NewLogger creates a new Logger instance.
// It takes the log channel, a source identifier (e.g., "ModbusReader-LINE01"),
// and a map of default details that will be included with every log message.
func NewLogger(errorLogChan chan<- messages.LogErrorMessage, source string, defaultDetails map[string]interface{}) *Logger {
	if errorLogChan == nil {
		log.Printf("[logger] 为源 '%s' 创建的 NewLogger 使用了空的 errorLogChan。日志将仅打印到标准输出。", source)
	}
	return &Logger{
		errorLogChan:   errorLog<PERSON>han,
		source:         source,
		defaultDetails: defaultDetails,
	}
}

// Send constructs and sends a log message.
// It merges the logger's default details with any extra details provided at call time.
func (l *Logger) Send(severity messages.SeverityType, message string, extraDetails map[string]interface{}) {
	// Create the final details map, starting with a copy of the defaults
	finalDetails := make(map[string]interface{})
	for k, v := range l.defaultDetails {
		finalDetails[k] = v
	}

	// Merge in any extra details provided, overriding defaults if keys conflict
	if extraDetails != nil {
		for k, v := range extraDetails {
			finalDetails[k] = v
		}
	}

	logMsg := messages.LogErrorMessage{
		BaseMessage: messages.BaseMessage{Type: messages.TaskLogEvent, Timestamp: time.Now()},
		Source:      l.source,
		Severity:    severity,
		Message:     message,
		Details:     finalDetails,
	}

	if l.errorLogChan == nil {
		log.Printf("[logger-%s] 日志通道为空。正在丢弃日志消息: %s", l.source, message)
		return
	}

	select {
	case l.errorLogChan <- logMsg:
		// Message sent successfully
	default:
		// This default case prevents the logger from blocking if the channel is full.
		log.Printf("[logger-%s] 发送日志到 errorLogChan 失败 (通道已满)。正在丢弃消息: %s", l.source, message)
	}
}
