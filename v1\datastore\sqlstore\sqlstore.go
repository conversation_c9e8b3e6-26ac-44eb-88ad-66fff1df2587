package sqlstore

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"modbus-app/backend/v1/config"
	"modbus-app/backend/v1/datastore"
	"modbus-app/backend/v1/messages"

	// SQL Server driver
	_ "github.com/denisenkom/go-mssqldb"
)

// SQLStore implements the datastore.Datastore interface using a SQL Server database.
// It holds the database connection pool and configuration.
type SQLStore struct {
	db    *sql.DB
	dbCfg config.DatabaseConfig // Store db config for timeouts and other direct use
}

// InitializeDatabase sets up the database connection pool.
// This function is typically called once at application startup.
func InitializeDatabase(cfg config.DatabaseConfig) (*sql.DB, error) {
	connString := fmt.Sprintf("sqlserver://%s:%s@%s:%d?database=%s&connection+timeout=%d",
		cfg.User, cfg.Password, cfg.Server, cfg.Port, cfg.Database, cfg.Timeout)

	db, err := sql.Open("sqlserver", connString)
	if err != nil {
		return nil, fmt.Errorf("[Main]: 数据库驱动打开失败: %w", err)
	}

	// Use a context with the configured connection timeout for the initial ping.
	pingCtx, cancel := context.WithTimeout(context.Background(), time.Duration(cfg.Timeout)*time.Second)
	defer cancel()

	err = db.PingContext(pingCtx)
	if err != nil {
		// If ping fails, close the potentially problematic pool and return the error.
		db.Close() // Best effort to close
		return nil, fmt.Errorf("[Main]: 数据库连接测试失败 (Server: %s, Database: %s): %w", cfg.Server, cfg.Database, err)
	}

	log.Println("[Main]: 数据库连接池初始化并测试成功。")
	return db, nil
}

// NewSQLStore creates a new SQLStore instance.
// It expects an already initialized *sql.DB connection pool.
func NewSQLStore(db *sql.DB, dbCfg config.DatabaseConfig) *SQLStore {
	return &SQLStore{
		db:    db,
		dbCfg: dbCfg,
	}
}

// Close closes the underlying database connection pool.
// This should be called on application shutdown.
func (s *SQLStore) Close() error {
	if s.db != nil {
		log.Println("[Main]: 关闭数据库连接池...")
		return s.db.Close()
	}
	log.Println("[Main]: 数据库连接池已经是nil，无需关闭。")
	return nil
}

// Connect for SQLStore could be a no-op if InitializeDatabase handles connection setup,
// or it could perform additional checks. For now, it satisfies the interface.
// The actual connection is established by InitializeDatabase.
func (s *SQLStore) Connect() error {
	if s.db == nil {
		return fmt.Errorf("[Main]: 数据库未初始化，无法连接。")
	}
	// Ping to ensure connection is still alive
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(s.dbCfg.Timeout)*time.Second)
	defer cancel()
	err := s.db.PingContext(ctx)
	if err != nil {
		return fmt.Errorf("[Main]: 数据库连接测试失败: %w", err)
	}
	log.Println("[Main]: 数据库连接已存在且测试通过。")
	return nil
}

// UpsertRealTimeData 将实时数据通过调用 dbo.sp_UpsertRealTimeData 存储过程写入数据库。
func (s *SQLStore) UpsertRealTimeData(msg messages.RealtimeDataMessage) error {
	if s.db == nil {
		log.Printf("[SQLDataStore] 更新实时数据: 数据库连接为 nil。LineCode: %s", msg.LineCode)
		return fmt.Errorf("[SQLDataStore] 更新实时数据: 数据库未连接。")
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(s.dbCfg.Timeout)*time.Second)
	defer cancel()

	// 参数准备
	attDate := msg.RealDatetime.Format("2006-01-02") // YYYY-MM-DD 格式
	lineCode := msg.LineCode
	taskNum := "ALLINONE" // 硬编码
	updatedDatetime := msg.RealDatetime

	// 使用-999作为"不更新"标志，支持独立更新input和output
	// 如果消息中的值是默认值（通常是0），我们需要判断是否真的要更新
	regInput := msg.Input
	regInputTime := msg.InputTime
	regOutput := msg.Output
	regOutputTime := msg.OutputTime

	// PLC状态字段：-1是有效值（如午夜重置），-999表示不更新
	lastPlcInput := msg.AppLastKnownPLCInput
	lastPlcOutput := msg.AppLastKnownPLCOutput

	// 基准值字段：-1是有效值，-999表示不更新
	dailyBaselineInput := msg.DailyBaselineInput
	dailyBaselineOutput := msg.DailyBaselineOutput

	// 补偿值字段：0是有效值，-999表示不更新
	resetCompensationInput := msg.ResetCompensationInput
	resetCompensationOutput := msg.ResetCompensationOutput

	log.Printf("[SQLDataStore-%s] 更新实时数据: AttDate: %s, TaskNum: %s, Input: %d, InputTime: %d, Output: %d, OutputTime: %d, LastPlcInput: %d, LastPlcOutput: %d, BaselineInput: %d, BaselineOutput: %d, CompensationInput: %d, CompensationOutput: %d, UpdatedAt: %s",
		lineCode, attDate, taskNum, regInput, regInputTime, regOutput, regOutputTime, lastPlcInput, lastPlcOutput, dailyBaselineInput, dailyBaselineOutput, resetCompensationInput, resetCompensationOutput, updatedDatetime.Format("2006-01-02 15:04:05.000"))

	_, execErr := s.db.ExecContext(ctx, "EXEC dbo.sp_UpsertRealTimeData_WithBaseline @att_date, @line_code, @task_num, @reg_input, @reg_input_time, @reg_output, @reg_output_time, @updated_datetime, @last_plc_input, @last_plc_output, @daily_baseline_input, @daily_baseline_output, @reset_compensation_input, @reset_compensation_output",
		sql.Named("att_date", attDate),
		sql.Named("line_code", lineCode),
		sql.Named("task_num", taskNum),
		sql.Named("reg_input", regInput),
		sql.Named("reg_input_time", regInputTime),
		sql.Named("reg_output", regOutput),
		sql.Named("reg_output_time", regOutputTime),
		sql.Named("updated_datetime", updatedDatetime),
		sql.Named("last_plc_input", lastPlcInput),
		sql.Named("last_plc_output", lastPlcOutput),
		sql.Named("daily_baseline_input", dailyBaselineInput),
		sql.Named("daily_baseline_output", dailyBaselineOutput),
		sql.Named("reset_compensation_input", resetCompensationInput),
		sql.Named("reset_compensation_output", resetCompensationOutput),
	)

	if execErr != nil {
		log.Printf("[SQLDataStore-%s] 更新实时数据: 失败。Error: %v. Args: att_date=%s, line_code=%s, task_num=%s, reg_input=%d, reg_input_time=%d, reg_output=%d, reg_output_time=%d, updated_datetime=%s, last_plc_input=%d, last_plc_output=%d",
			msg.LineCode, execErr, attDate, lineCode, taskNum, regInput, regInputTime, regOutput, regOutputTime, updatedDatetime, lastPlcInput, lastPlcOutput)
		if execErr == context.DeadlineExceeded {
			return fmt.Errorf("[SQLDataStore] 更新实时数据: 超时 (LineCode: %s): %w", msg.LineCode, execErr)
		}
		return fmt.Errorf("[SQLDataStore] 更新实时数据: 失败 (LineCode: %s): %w", msg.LineCode, execErr)
	}

	log.Printf("[SQLDataStore-%s] 更新实时数据: 成功。", msg.LineCode)
	return nil
}

// GetLatestState 从数据库获取指定产线和日期的最新状态，包括累计值和对应的PLC原始值。
// 它现在通过完整的 ManagedLineInfo 来确保查询的唯一性。
// 如果没有找到当天的记录，它会返回 -1 值和 nil 错误。
func (s *SQLStore) GetLatestState(
	lineInfo datastore.ManagedLineInfo, forDate time.Time,
) (
	latestCumulativeInput int, latestPlcInput int,
	latestCumulativeOutput int, latestPlcOutput int,
	err error,
) {
	// 初始化返回值为 -1，这是"未找到"或"不适用"的默认状态
	latestCumulativeInput = -1
	latestPlcInput = -1
	latestCumulativeOutput = -1
	latestPlcOutput = -1

	if s.db == nil {
		err = fmt.Errorf("[SQLDataStore] 获取最新状态: 数据库未连接")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(s.dbCfg.Timeout)*time.Second)
	defer cancel()

	// 增加了 server_ip 和各个地址字段来确保唯一性
	query := `
        SELECT TOP 1 
            reg_input, 
            last_plc_input,
            reg_output,
            last_plc_output
        FROM 
            dbo.U_MesLinesRealTimeData -- 根据用户提供的存储过程，确认表名
        WHERE 
            line_code = @p1 
            AND att_date = @p2 
        ORDER BY 
            updated_datetime DESC;
    `

	attDate := forDate.Format("2006-01-02")

	// 使用 sql.NullInt32 来正确处理数据库中的 NULL 值
	var dbCumulInput, dbPlcInput, dbCumulOutput, dbPlcOutput sql.NullInt32

	row := s.db.QueryRowContext(ctx, query,
		lineInfo.LineCode,
		attDate,
	)
	queryErr := row.Scan(&dbCumulInput, &dbPlcInput, &dbCumulOutput, &dbPlcOutput)

	if queryErr != nil {
		if queryErr == sql.ErrNoRows {
			// 没有找到当天的记录是正常情况，不是一个真正的错误。
			// 函数将返回初始化的 -1 值。
			return latestCumulativeInput, latestPlcInput, latestCumulativeOutput, latestPlcOutput, nil
		}
		// 其他查询错误
		err = fmt.Errorf("[SQLDataStore-%s] 获取最新状态: 查询失败 (Date: %s): %w", lineInfo.LineCode, attDate, queryErr)
		return
	}

	// 如果查询成功，用数据库返回的值覆盖默认的 -1
	if dbCumulInput.Valid {
		latestCumulativeInput = int(dbCumulInput.Int32)
	}
	if dbPlcInput.Valid {
		latestPlcInput = int(dbPlcInput.Int32)
	}
	if dbCumulOutput.Valid {
		latestCumulativeOutput = int(dbCumulOutput.Int32)
	}
	if dbPlcOutput.Valid {
		latestPlcOutput = int(dbPlcOutput.Int32)
	}

	return
}

// GetLatestStateWithBaseline 从数据库获取指定产线和日期的最新状态，包括累计值、PLC原始值、基准值和补偿值
// 这个方法扩展了GetLatestState，增加了基准值和补偿值的恢复功能
func (s *SQLStore) GetLatestStateWithBaseline(
	lineInfo datastore.ManagedLineInfo, forDate time.Time,
) (
	latestCumulativeInput int, latestPlcInput int,
	latestCumulativeOutput int, latestPlcOutput int,
	baselineInput int, baselineOutput int,
	compensationInput int, compensationOutput int,
	err error,
) {
	// 初始化返回值
	latestCumulativeInput = -1
	latestPlcInput = -1
	latestCumulativeOutput = -1
	latestPlcOutput = -1
	baselineInput = -1
	baselineOutput = -1
	compensationInput = 0
	compensationOutput = 0

	if s.db == nil {
		err = fmt.Errorf("[SQLDataStore] 获取最新状态: 数据库未连接")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(s.dbCfg.Timeout)*time.Second)
	defer cancel()

	query := `
        SELECT TOP 1
            reg_input,
            last_plc_input,
            reg_output,
            last_plc_output,
            ISNULL(daily_baseline_input, -1) as baseline_input,
            ISNULL(daily_baseline_output, -1) as baseline_output,
            ISNULL(reset_compensation_input, 0) as compensation_input,
            ISNULL(reset_compensation_output, 0) as compensation_output
        FROM
            dbo.U_MesLinesRealTimeData
        WHERE
            line_code = @p1 AND att_date = @p2
        ORDER BY
            updated_datetime DESC;
    `

	attDate := forDate.Format("2006-01-02")

	var dbCumulInput, dbPlcInput, dbCumulOutput, dbPlcOutput sql.NullInt32
	var dbBaselineInput, dbBaselineOutput, dbCompensationInput, dbCompensationOutput sql.NullInt32

	row := s.db.QueryRowContext(ctx, query, lineInfo.LineCode, attDate)
	queryErr := row.Scan(
		&dbCumulInput, &dbPlcInput, &dbCumulOutput, &dbPlcOutput,
		&dbBaselineInput, &dbBaselineOutput, &dbCompensationInput, &dbCompensationOutput,
	)

	if queryErr != nil {
		if queryErr == sql.ErrNoRows {
			// 没有找到当天的记录是正常情况，返回初始化的值
			return
		}
		err = fmt.Errorf("[SQLDataStore] 获取最新状态失败: %w", queryErr)
		return
	}

	// 设置返回值
	if dbCumulInput.Valid {
		latestCumulativeInput = int(dbCumulInput.Int32)
	}
	if dbPlcInput.Valid {
		latestPlcInput = int(dbPlcInput.Int32)
	}
	if dbCumulOutput.Valid {
		latestCumulativeOutput = int(dbCumulOutput.Int32)
	}
	if dbPlcOutput.Valid {
		latestPlcOutput = int(dbPlcOutput.Int32)
	}
	if dbBaselineInput.Valid {
		baselineInput = int(dbBaselineInput.Int32)
	}
	if dbBaselineOutput.Valid {
		baselineOutput = int(dbBaselineOutput.Int32)
	}
	if dbCompensationInput.Valid {
		compensationInput = int(dbCompensationInput.Int32)
	}
	if dbCompensationOutput.Valid {
		compensationOutput = int(dbCompensationOutput.Int32)
	}

	return
}

// LogErrorToDB 将错误日志信息插入到数据库的 U_MesLinesError_T 表中。
func (s *SQLStore) LogErrorToDB(logMsg messages.LogErrorMessage) error {
	if s.db == nil {
		log.Println("[SQLDataStore] 错误日志: 数据库连接为 nil。")
		return fmt.Errorf("[SQLDataStore] 错误日志: 数据库未连接。")
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(s.dbCfg.Timeout)*time.Second)
	defer cancel()

	// 1. 处理 Details 用于组合 err_msg
	var detailsForCompositeMsg string
	if logMsg.Details == nil {
		detailsForCompositeMsg = ""
	} else {
		detailsBytes, err := json.Marshal(logMsg.Details)
		if err != nil {
			log.Printf("[SQLDataStore] 错误日志: 无法序列化 Details 为 JSON: %v. 原始 Details: %+v", err, logMsg.Details)
			detailsForCompositeMsg = "" // 序列化失败则为空字符串
		} else {
			jsonStr := string(detailsBytes)
			if jsonStr == "null" { // 例如 map 为 nil 的情况
				detailsForCompositeMsg = ""
			} else {
				detailsForCompositeMsg = jsonStr // 如 "{}" 或 "{\"key\":\"value\"}"
			}
		}
	}

	// 2. 提取或设置各列的值
	// server_ip (NOT NULL)
	serverIPVal := ""
	if val, ok := logMsg.Details["server_ip"].(string); ok {
		serverIPVal = val
	}

	// line_code (NOT NULL)
	lineCodeVal := ""
	if strings.HasPrefix(logMsg.Source, "ModbusReader-") { // 尝试从Source解析
		parts := strings.SplitN(logMsg.Source, "-", 2)
		if len(parts) == 2 && parts[1] != "" {
			lineCodeVal = parts[1]
		}
	}
	if lc, ok := logMsg.Details["line_code"].(string); ok && lc != "" { // Details中的值优先
		lineCodeVal = lc
	}

	// line_name (NOT NULL)
	lineNameVal := ""
	if val, ok := logMsg.Details["line_name"].(string); ok {
		lineNameVal = val
	}

	// modbus_ip (NOT NULL)
	modbusIPVal := ""
	if val, ok := logMsg.Details["modbus_ip"].(string); ok {
		modbusIPVal = val
	}

	// modbus_port (NOT NULL, default 0 if not found)
	modbusPortVal := 0
	if val, ok := logMsg.Details["modbus_port"]; ok {
		switch v := val.(type) {
		case float64: // JSON 数字通常为 float64
			modbusPortVal = int(v)
		case int:
			modbusPortVal = v
		case string:
			parsed, err := strconv.Atoi(v)
			if err == nil {
				modbusPortVal = parsed
			}
		}
	}

	// modbus_slave_id (NOT NULL, default 0 if not found)
	modbusSlaveIDVal := 0
	if val, ok := logMsg.Details["modbus_slave_id"]; ok {
		switch v := val.(type) {
		case float64:
			modbusSlaveIDVal = int(v)
		case int:
			modbusSlaveIDVal = v
		case string:
			parsed, err := strconv.Atoi(v)
			if err == nil {
				modbusSlaveIDVal = parsed
			}
		}
	}

	// modbus_timeout (NOT NULL, default 0 if not found)
	modbusTimeoutVal := 0
	if val, ok := logMsg.Details["modbus_timeout"]; ok {
		switch v := val.(type) {
		case float64:
			modbusTimeoutVal = int(v)
		case int:
			modbusTimeoutVal = v
		case string:
			parsed, err := strconv.Atoi(v)
			if err == nil {
				modbusTimeoutVal = parsed
			}
		}
	}

	// severityStr 用于组合 err_msg (表U_MesLinesError_T无独立Severity列)
	// 根据之前的讨论，如果无法确定Severity，则为空字符串
	severityStr := ""
	// 可选: 如果 messages.SeverityType 有String()方法或可转换为已知int, 可在此处赋值
	// switch logMsg.Severity { ... }

	// err_msg (NOT NULL)
	compositeErrorMessage := fmt.Sprintf("Source: %s | Severity: %s | Message: %s | Details: %s",
		logMsg.Source,
		severityStr, // 将为空字符串 "" 或解析后的值
		logMsg.Message,
		detailsForCompositeMsg,
	)
	if len(compositeErrorMessage) > 2000 { // 遵守表定义 VARCHAR(2000)
		compositeErrorMessage = compositeErrorMessage[:2000]
	}

	// created_datetime (NOT NULL)
	createdDatetimeVal := logMsg.Timestamp // 使用消息自带的时间戳

	// 3. 构建并执行 INSERT 语句
	query := `
		INSERT INTO dbo.U_MesLinesError (
			server_ip, modbus_ip, modbus_port, modbus_slave_id, modbus_timeout,
			line_code, line_name, err_msg, created_datetime
		) VALUES (@p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9);
	`

	_, execErr := s.db.ExecContext(ctx, query,
		serverIPVal,           // @p1
		modbusIPVal,           // @p2
		modbusPortVal,         // @p3
		modbusSlaveIDVal,      // @p4
		modbusTimeoutVal,      // @p5
		lineCodeVal,           // @p6
		lineNameVal,           // @p7
		compositeErrorMessage, // @p8
		createdDatetimeVal,    // @p9
	)

	if execErr != nil {
		// 保持详细的错误日志
		log.Printf("[SQLDataStore] 错误日志: 执行插入语句失败。Query: %s, Args: [%v, %v, %d, %d, %d, %v, %v, %s, %v], Error: %v",
			query, serverIPVal, modbusIPVal, modbusPortVal, modbusSlaveIDVal, modbusTimeoutVal, lineCodeVal, lineNameVal, compositeErrorMessage, createdDatetimeVal, execErr)
		if execErr == context.DeadlineExceeded {
			return fmt.Errorf("[SQLDataStore] 错误日志: 数据库插入超时: %w", execErr)
		}
		return fmt.Errorf("[SQLDataStore] 错误日志: 数据库插入失败: %w", execErr)
	}

	return nil
}

// GetManagedLinesByServerIPs retrieves lines managed by the given server IPs.
func (s *SQLStore) GetManagedLinesByServerIPs(serverIPs []string) ([]datastore.ManagedLineInfo, error) {
	if s.db == nil {
		return nil, fmt.Errorf("[SQLDataStore] 获取配置信息: 数据库未连接")
	}

	if len(serverIPs) == 0 {
		return []datastore.ManagedLineInfo{}, nil // Return empty slice if no IPs are provided
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(s.dbCfg.Timeout)*time.Second)
	defer cancel()

	// Build the IN clause for the query
	params := make([]interface{}, len(serverIPs))
	for i, ip := range serverIPs {
		params[i] = ip
	}
	// Note: go-mssqldb uses @p1, @p2, etc. for placeholders. We build them manually.
	inClause := "IN ("
	for i := 0; i < len(serverIPs); i++ {
		if i > 0 {
			inClause += ","
		}
		inClause += fmt.Sprintf("@p%d", i+1)
	}
	inClause += ")"

	query := `
        SELECT 
            id,
            line_code,
            line_name,
            server_ip,
            plc_protocol,
            modbus_ip as plc_ip_address,
            modbus_port as plc_port,
            modbus_slave_id as plc_slave_id,
            modbus_timeout as plc_timeout_sec,
            input_address,
            input_time_address,
            output_address,
            output_time_address,
            created_datetime,
            updated_datetime
        FROM 
            dbo.VV_MesLinesController
        WHERE 
            server_ip ` + inClause

	rows, err := s.db.QueryContext(ctx, query, params...)
	if err != nil {
		return nil, fmt.Errorf("[SQLDataStore] 获取配置信息: 查询管理的生产线失败 (IPs: %v): %w", serverIPs, err)
	}
	defer rows.Close()

	var lines []datastore.ManagedLineInfo
	for rows.Next() {
		var line datastore.ManagedLineInfo
		// Use sql.NullString etc. for potentially null columns to avoid scan errors
		var (
			id                sql.NullInt64
			lineCode          sql.NullString
			lineName          sql.NullString
			serverIP          sql.NullString
			plcProtocol       sql.NullString
			plcIPAddress      sql.NullString
			plcPort           sql.NullInt64
			plcSlaveID        sql.NullInt64
			plcTimeoutSec     sql.NullInt64
			inputAddress      sql.NullString
			inputTimeAddress  sql.NullString
			outputAddress     sql.NullString
			outputTimeAddress sql.NullString
			createdDatetime   sql.NullTime
			updatedDatetime   sql.NullTime
		)

		if err := rows.Scan(
			&id, &lineCode, &lineName, &serverIP,
			&plcProtocol, &plcIPAddress, &plcPort, &plcSlaveID, &plcTimeoutSec,
			&inputAddress, &inputTimeAddress, &outputAddress, &outputTimeAddress,
			&createdDatetime, &updatedDatetime,
		); err != nil {
			log.Printf("[SQLDataStore] 获取配置信息: 扫描生产线数据失败: %v", err)
			continue // Skip to next row on scan error
		}

		// Populate the struct, handling nulls
		line.ProductionLineID = int(id.Int64)
		line.LineCode = lineCode.String
		line.LineName = lineName.String
		line.ServerIP = serverIP.String
		line.PlcProtocol = plcProtocol.String
		line.PlcIPAddress = plcIPAddress.String
		line.PlcPort = int(plcPort.Int64)
		line.PlcSlaveID = int(plcSlaveID.Int64)
		line.PlcTimeoutSec = int(plcTimeoutSec.Int64)
		line.InputAddress = inputAddress.String
		line.InputTimeAddress = inputTimeAddress.String
		line.OutputAddress = outputAddress.String
		line.OutputTimeAddress = outputTimeAddress.String
		line.CreatedDatetime = createdDatetime.Time
		line.UpdatedDatetime = updatedDatetime.Time

		lines = append(lines, line)
	}

	if err = rows.Err(); err != nil {
		log.Printf("[SQLDataStore] 获取配置信息: 遍历生产线数据行时出错: %v", err)
		return nil, err
	}

	return lines, nil
}

// StartListeners 启动后台 goroutines 来监听错误日志和实时数据 channels。
func (s *SQLStore) StartListeners(
	ctx context.Context, // Application context for graceful shutdown
	wg *sync.WaitGroup, // WaitGroup to signal listener completion
	errorLogChan <-chan messages.LogErrorMessage, // Channel for receiving error logs
	dataWriteChan <-chan messages.RealtimeDataMessage, // Channel for receiving real-time data
	// midnightTaskChan <-chan messages.MidnightResetMessage, // Placeholder for future use
) {
	log.Println("[SQLDataStore] 启动 listeners...")

	// Listener for error logs
	// 注意：wg.Add(1)已在调用方(main.go)中完成，这里不需要重复调用
	go func() {
		defer wg.Done()
		log.Println("[SQLDataStore] 错误日志: listener goroutine 已启动。")
		for {
			select {
			case logMsg, ok := <-errorLogChan:
				if !ok {
					log.Println("[SQLDataStore] 错误日志: ErrorLogChannel 已关闭，错误日志 listener 退出。")
					return
				}
				// 调用LogErrorToDB处理日志。注意：LogErrorToDB本身不应该阻塞太久。
				if err := s.LogErrorToDB(logMsg); err != nil {
					// 如果LogErrorToDB失败，记录到标准输出（或备用日志系统）
					log.Printf("[SQLDataStore] 错误日志: 记录 LogErrorMessage 到数据库失败: %v. 原始消息: %+v", err, logMsg)
				}
			case <-ctx.Done():
				log.Println("[SQLDataStore] 错误日志: 收到关闭信号，错误日志 listener 正在退出...")
				// 处理在退出前可能仍在 channel 中的消息 (可选)
				// for logMsg := range errorLogChan { ... }
				log.Println("[SQLDataStore] 错误日志: 错误日志 listener 已停止。")
				return
			}
		}
	}()

	// Listener for real-time data
	// 注意：wg.Add(1)已在调用方(main.go)中完成，这里不需要重复调用
	go func() {
		defer wg.Done()
		log.Println("[SQLDataStore] 实时数据: listener goroutine 已启动。")
		for {
			select {
			case dataMsg, ok := <-dataWriteChan:
				if !ok {
					log.Println("[SQLDataStore] 实时数据: DataWriteChannel 已关闭，实时数据 listener 退出。")
					return
				}
				// 调用UpsertRealTimeData处理数据。注意：UpsertRealTimeData本身不应该阻塞太久。
				if err := s.UpsertRealTimeData(dataMsg); err != nil {
					// 如果UpsertRealTimeData失败，记录到标准输出（或错误日志channel）
					log.Printf("[SQLDataStore] 实时数据: 更新数据库失败: %v. 原始消息: %+v", err, dataMsg)
				}
			case <-ctx.Done():
				log.Println("[SQLDataStore] 实时数据: 收到关闭信号，实时数据 listener 正在退出...")
				// 处理在退出前可能仍在 channel 中的消息 (可选)
				log.Println("[SQLDataStore] 实时数据: 实时数据 listener 已停止。")
				return
			}
		}
	}()

	// TODO: Listener for midnight tasks (if/when midnightTaskChan is added)

	log.Println("[SQLDataStore]: 所有 listeners goroutines 已被安排启动。")
}
