package scheduler

import (
	"context"
	"fmt"
	"log"
	"sort"
	"sync"
	"time"

	"modbus-app/backend/v1/config"
	"modbus-app/backend/v1/messages"
	// "modbus-app/backend/v1/utils" // If time zone utilities are needed
)

// internalScheduledTask wraps a ScheduledTaskConfig with its next runtime.
type internalScheduledTask struct {
	config.ScheduledTaskConfig
	NextRunTime time.Time
	// TODO: Consider adding a field for the actual target channel if resolved early,
	// or ensure robust lookup in taskChannels map during dispatch.
}

// byNextRunTime implements sort.Interface for []internalScheduledTask based on NextRunTime.
type byNextRunTime []internalScheduledTask

func (a byNextRunTime) Len() int           { return len(a) }
func (a byNextRunTime) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a byNextRunTime) Less(i, j int) bool { return a[i].NextRunTime.Before(a[j].NextRunTime) }

// Scheduler manages time-based tasks and daily schedules.
type Scheduler struct {
	config         config.SchedulerConfig
	errorLogChan   chan<- messages.LogErrorMessage
	taskChannels   map[string]chan<- interface{} // Key: ScheduledTaskConfig.TargetChannelName
	scheduledTasks []internalScheduledTask
	location       *time.Location
	// logger *log.Logger // Consider a dedicated logger
}

// NewScheduler creates a new Scheduler instance.
func NewScheduler(
	cfg config.SchedulerConfig,
	errorLogChan chan<- messages.LogErrorMessage,
	taskChannels map[string]chan<- interface{},
) (*Scheduler, error) {
	if errorLogChan == nil {
		return nil, fmt.Errorf("NewScheduler:错误日志channel不能为空")
	}
	if taskChannels == nil {
		log.Println("[Scheduler] NewScheduler: 任务channels映射为nil。自定义任务可能无法分发。")
		taskChannels = make(map[string]chan<- interface{}) // Initialize to empty map to avoid nil panics
	}

	loc, err := time.LoadLocation(cfg.Location)
	if err != nil {
		log.Printf("[Scheduler] NewScheduler: 加载时区 '%s' 失败: %v。将使用本地时区。", cfg.Location, err)
		loc = time.Local // Fallback to local time zone
	}

	// Helper to ensure valid cron-like values, converting out-of-range to -1 (wildcard)
	validateCronField := func(value, min, max int) int {
		if value >= min && value <= max {
			return value
		}
		return -1 // Treat as wildcard if out of specified range (e.g., Hour 25 becomes wildcard)
	}

	s := &Scheduler{
		config:         cfg,
		errorLogChan:   errorLogChan,
		taskChannels:   taskChannels,
		location:       loc,
		scheduledTasks: make([]internalScheduledTask, 0, len(cfg.Tasks)),
	}

	log.Printf("[Scheduler] NewScheduler: 正在使用 %d 个任务在时区 %s 中进行初始化...", len(cfg.Tasks), s.location.String())

	now := time.Now().In(s.location)
	for _, taskCfg := range cfg.Tasks {
		// Validate task configuration (e.g., target channel existence)
		if taskCfg.TargetChannel != "" {
			if _, ok := s.taskChannels[taskCfg.TargetChannel]; !ok {
				log.Printf("[Scheduler] NewScheduler: 任务 '%s' 指定了目标channel '%s', 但此channel未在taskChannels中提供。该任务可能无法分发。", taskCfg.ID, taskCfg.TargetChannel)
				// Continue processing other tasks, but this one is effectively disabled if it needs a channel.
			}
		}

		taskCfg.Hour = validateCronField(taskCfg.Hour, 0, 23)
		taskCfg.Minute = validateCronField(taskCfg.Minute, 0, 59)
		taskCfg.Second = validateCronField(taskCfg.Second, 0, 59)

		nextRun := calculateNextRunTime(now, taskCfg, s.location)
		s.scheduledTasks = append(s.scheduledTasks, internalScheduledTask{
			ScheduledTaskConfig: taskCfg,
			NextRunTime:         nextRun,
		})
		log.Printf("[Scheduler] 任务 '%s' 已加载。初始下次运行时间: %s", taskCfg.ID, nextRun.Format(time.RFC3339))
	}

	sort.Sort(byNextRunTime(s.scheduledTasks))
	if len(s.scheduledTasks) > 0 {
		log.Printf("[Scheduler] 所有任务已加载并排序。下一个任务 '%s' 将在 %s 执行", s.scheduledTasks[0].ID, s.scheduledTasks[0].NextRunTime.Format(time.RFC3339))
	} else {
		log.Println("[Scheduler] 未加载任何任务。")
	}

	log.Println("[Scheduler] NewScheduler: 初始化成功。")
	return s, nil
}

// calculateNextRunTime calculates the next run time for a task based on its H, M, S configuration (where -1 is a wildcard)
// and the provided location, starting the search from strictly after searchAfterTime.
// It searches up to a maximum of 2 days into the future to prevent excessively long searches for misconfigurations.
func calculateNextRunTime(searchAfterTime time.Time, taskCfg config.ScheduledTaskConfig, loc *time.Location) time.Time {
	// Normalize task config values: if a higher-order unit is a wildcard, lower-order units effectively become wildcards for matching purposes
	// unless they are the *most specific* non-wildcard unit.
	// Example: H=-1, M=30, S=0 means every hour at 30 minutes and 0 seconds.
	// Example: H=10, M=-1, S=0 means every minute of the 10th hour, at 0 seconds.
	// Example: H=10, M=-1, S=-1 means every second of every minute of the 10th hour.

	// Start searching from the next second after searchAfterTime
	currentAttemptTime := searchAfterTime.In(loc).Add(time.Second).Truncate(time.Second)
	maxSearchTime := searchAfterTime.AddDate(0, 0, 2) // Search up to 2 days ahead

	for !currentAttemptTime.After(maxSearchTime) {
		hourMatch := (taskCfg.Hour == -1) || (currentAttemptTime.Hour() == taskCfg.Hour)
		minuteMatch := (taskCfg.Minute == -1) || (currentAttemptTime.Minute() == taskCfg.Minute)
		secondMatch := (taskCfg.Second == -1) || (currentAttemptTime.Second() == taskCfg.Second)

		if hourMatch && minuteMatch && secondMatch {
			// Ensure we are strictly after the searchAfterTime in the same location for fair comparison
			if currentAttemptTime.After(searchAfterTime.In(loc)) {
				return currentAttemptTime // Found the next valid run time
			}
		}
		currentAttemptTime = currentAttemptTime.Add(time.Second) // Move to the next second
	}

	// Should not be reached if configuration is valid and within 2 days.
	// This indicates a problem or a very sparse schedule beyond 2 days.
	log.Printf("[Scheduler] calculateNextRunTime: 未能在未来2天内为任务ID '%s' (H:%d M:%d S:%d) 找到下一个运行时间。返回一个遥远的未来时间以防止立即重复运行。", taskCfg.ID, taskCfg.Hour, taskCfg.Minute, taskCfg.Second)
	return searchAfterTime.AddDate(10, 0, 0) // Return a time far in the future
}

// Start begins the scheduler's operation.
// It processes tasks from its internal list and manages their execution timing.
func (s *Scheduler) Start(ctx context.Context, wg *sync.WaitGroup) {
	defer wg.Done()
	log.Println("[Scheduler] Start: 启动调度器 goroutine...")

	if len(s.scheduledTasks) == 0 {
		log.Println("[Scheduler] 无计划任务。调度器将闲置直至关闭。")
		<-ctx.Done() // Wait for shutdown signal
		log.Println("[Scheduler] 关闭: 无任务被调度。调度器已停止。")
		return
	}

	// Initial timer setup for the first task
	var timer *time.Timer
	firstTask := s.scheduledTasks[0]
	durationUntilFirstTask := time.Until(firstTask.NextRunTime.In(s.location))
	if durationUntilFirstTask < 0 { // Should ideally not happen if calculateNextRunTime is correct
		durationUntilFirstTask = 0
		log.Printf("[Scheduler] 首个任务 '%s' 的下次运行时间 %s 已是过去。将立即运行。", firstTask.ID, firstTask.NextRunTime.Format(time.RFC3339))
	}
	timer = time.NewTimer(durationUntilFirstTask)
	log.Printf("[Scheduler] 下一个任务 '%s' 已计划在 %v 后于 %s 执行", firstTask.ID, durationUntilFirstTask, firstTask.NextRunTime.Format(time.RFC3339))

	for {
		select {
		case <-timer.C:
			now := time.Now().In(s.location)
			var tasksToRun []internalScheduledTask
			var remainingTasks []internalScheduledTask

			// Identify tasks to run
			for i, task := range s.scheduledTasks {
				if !task.NextRunTime.After(now) { // If task is due (NextRunTime is now or in the past)
					tasksToRun = append(tasksToRun, task)
					if task.OneTime {
						log.Printf("[Scheduler] 任务 '%s' 是一次性任务且已运行。将被移除。", task.ID)
					} else {
						// Reschedule periodic task. searchAfterTime is the time this task was supposed to run.
						task.NextRunTime = calculateNextRunTime(task.NextRunTime, task.ScheduledTaskConfig, s.location)
						remainingTasks = append(remainingTasks, task)
						log.Printf("[Scheduler] 任务 '%s' (周期性) 已重新计划。新的下次运行时间: %s", task.ID, task.NextRunTime.Format(time.RFC3339))
					}
				} else {
					// This task and subsequent tasks are not yet due (since list is sorted)
					remainingTasks = append(remainingTasks, s.scheduledTasks[i:]...)
					break
				}
			}
			s.scheduledTasks = remainingTasks
			sort.Sort(byNextRunTime(s.scheduledTasks)) // Re-sort if periodic tasks were updated

			// Dispatch due tasks
			for _, task := range tasksToRun {
				s.dispatchTask(task)
			}

			// Reset timer for the next task, or wait for shutdown if no tasks left
			if len(s.scheduledTasks) == 0 {
				log.Println("[Scheduler] 所有任务已完成或移除。调度器闲置直至关闭。")
				timer.Stop() // Stop the current timer
				<-ctx.Done() // Wait for shutdown, effectively ends the loop unless a break is added
				log.Println("[Scheduler] 关闭: 所有任务已处理。调度器正在停止...")
				return
			}

			// Set timer for the new earliest task
			nextTask := s.scheduledTasks[0]
			durationUntilNextTask := time.Until(nextTask.NextRunTime.In(s.location))
			if durationUntilNextTask < 0 {
				durationUntilNextTask = 0
			} // Safety for past due tasks
			timer.Reset(durationUntilNextTask)
			log.Printf("[Scheduler] 下一个任务 '%s' 重置为在 %v 后于 %s 执行", nextTask.ID, durationUntilNextTask, nextTask.NextRunTime.Format(time.RFC3339))

		case <-ctx.Done():
			log.Println("[Scheduler] 关闭: 收到关闭信号。调度器正在停止...")
			if timer != nil {
				timer.Stop()
			}
			log.Println("[Scheduler] 关闭: 调度器已停止。")
			return
		}
	}
}

func (s *Scheduler) dispatchTask(task internalScheduledTask) {
	log.Printf("[Scheduler] dispatchTask: 正在分发任务 ID: %s, 类型: %s, 目标: %s", task.ID, task.TaskMessageType, task.TargetChannel)

	targetChan, ok := s.taskChannels[task.TargetChannel]
	if !ok {
		if task.TargetChannel != "" { // Only log error if a target was specified but not found
			errMsg := fmt.Sprintf("任务 '%s' 的目标channel '%s' 未在taskChannels中找到。", task.TargetChannel, task.ID)
			s.sendLog(messages.SeverityError, errMsg, map[string]interface{}{"task_id": task.ID, "target_channel": task.TargetChannel})
		} else {
			// If no target channel is specified, it might be a task that doesn't dispatch (e.g. an internal marker, though not used currently)
			// or it's a type of task (like LOG_EVENT) that has a default handling (though current logic sends specific messages)
			log.Printf("[Scheduler] dispatchTask: 任务 '%s' 在配置中未指定TargetChannel。不进行分发。", task.ID)
		}
		return
	}

	var msgToSend interface{}
	baseMsg := messages.BaseMessage{Timestamp: time.Now()}

	switch messages.TaskType(task.TaskMessageType) {
	case messages.TaskMidnightReset: // Although we removed specific midnight *logic*, a task can still be of this type
		baseMsg.Type = messages.TaskMidnightReset
		msgToSend = messages.MidnightResetMessage{BaseMessage: baseMsg}
		log.Printf("[Scheduler] dispatchTask: 已为任务 '%s' 构建 MidnightResetMessage", task.ID)
	case messages.TaskHourlyForceSend:
		baseMsg.Type = messages.TaskHourlyForceSend
		msgToSend = messages.ForceSendLastKnownDataMessage{BaseMessage: baseMsg}
		log.Printf("[Scheduler] dispatchTask: 已为任务 '%s' 构建 ForceSendLastKnownDataMessage", task.ID)
	case messages.TaskCustomScheduled:
		baseMsg.Type = messages.TaskCustomScheduled
		msgToSend = messages.CustomScheduledTaskMessage{
			BaseMessage: baseMsg,
			TaskID:      task.ID,
			Payload:     task.Payload,
		}
		log.Printf("[Scheduler] dispatchTask: 已为任务 '%s' 构建 CustomScheduledTaskMessage", task.ID)
	default:
		errMsg := fmt.Sprintf("任务 '%s' 的任务消息类型 '%s' 未知或不受支持。", task.TaskMessageType, task.ID)
		s.sendLog(messages.SeverityError, errMsg, map[string]interface{}{"task_id": task.ID, "message_type": task.TaskMessageType})
		return
	}

	select {
	case targetChan <- msgToSend:
		s.sendLog(messages.SeverityInfo, fmt.Sprintf("任务 '%s' (类型: %s) 已成功分发到channel '%s'。", task.ID, task.TaskMessageType, task.TargetChannel), map[string]interface{}{"task_id": task.ID})
	default:
		// Non-blocking send failed
		errMsg := fmt.Sprintf("任务 '%s' (类型: %s) 分发到channel '%s' 失败: channel已满或已关闭。", task.ID, task.TaskMessageType, task.TargetChannel)
		s.sendLog(messages.SeverityWarning, errMsg, map[string]interface{}{"task_id": task.ID, "target_channel": task.TargetChannel})
	}
}

// sendLog is a helper to send log messages to the errorLogChannel.
func (s *Scheduler) sendLog(severity messages.SeverityType, message string, details map[string]interface{}) {
	if s.errorLogChan == nil {
		// This case should ideally be prevented by NewScheduler validation
		log.Printf("[Scheduler] sendLog: errorLogChan 为 nil。无法发送日志: %s", message)
		return
	}
	if details == nil {
		details = make(map[string]interface{})
	}
	details["scheduler_location"] = s.location.String()

	logMsg := messages.LogErrorMessage{
		BaseMessage: messages.BaseMessage{
			Type:      messages.TaskLogEvent,
			Timestamp: time.Now(),
		},
		Source:   "Scheduler",
		Severity: severity,
		Message:  message,
		Details:  details,
	}
	select {
	case s.errorLogChan <- logMsg:
	default:
		log.Printf("[Scheduler] sendLog: errorLogChan阻塞或已满。日志消息被丢弃: %s详情: %v", message, details)
	}
}
