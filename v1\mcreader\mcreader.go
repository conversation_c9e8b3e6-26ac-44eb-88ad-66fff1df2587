package mcreader

import (
	"context"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"sync"
	"time"

	"modbus-app/backend/v1/messages"
	"modbus-app/backend/v1/utils"
)

// CombinedMcReaderConfig holds all necessary runtime configurations for a <PERSON>c<PERSON>eader instance.
type CombinedMcReaderConfig struct {
	LineCode                   string
	PlcIPAddress               string
	PlcPort                    int
	ConnectTimeout             time.Duration
	ResponseTimeout            time.Duration
	PollingInterval            time.Duration
	FailureThreshold           int
	BackoffIntervalsSec        []int
	FailuresToNextBackoffLevel []int
	MaxConsecutiveFailures     int

	InputAddress      string
	InputTimeAddress  string
	OutputAddress     string
	OutputTimeAddress string
}

// Mc<PERSON>eader handles communication with a single Mitsubishi MC protocol device.
type McReader struct {
	config          CombinedMcReaderConfig
	client          MC
	shutdownChannel <-chan struct{}
	commandChannel  <-chan interface{}
	logger          *utils.Logger

	// All data and state logic is now delegated to the handler
	dataHandler *utils.CumulativeDataHandler
}

// addressRegex is used to parse addresses like "D100" or "M200".
var addressRegex = regexp.MustCompile(`^([A-Za-z]+)(\d+)$`)

// parseAddress splits a string like "D100" into device type "D" and address 100.
func parseAddress(addrStr string) (dev string, addr uint16, err error) {
	if addrStr == "" {
		return "", 0, nil // Not an error, just an empty address
	}
	matches := addressRegex.FindStringSubmatch(addrStr)
	if len(matches) != 3 {
		return "", 0, fmt.Errorf("地址格式无效: %s", addrStr)
	}

	dev = matches[1]
	addrInt, err := strconv.Atoi(matches[2])
	if err != nil {
		// The original error from Atoi is already in English, wrapping it provides context.
		return "", 0, fmt.Errorf("地址 '%s' 中的数字部分无效: %w", addrStr, err)
	}

	return dev, uint16(addrInt), nil
}

// NewMcReader creates and initializes a new McReader.
func NewMcReader(
	cfg CombinedMcReaderConfig,
	initialCumulativeInput int, initialAppLastKnownPLCInput int,
	initialCumulativeOutput int, initialAppLastKnownPLCOutput int,
	initialBaselineInput int, initialBaselineOutput int,
	initialCompensationInput int, initialCompensationOutput int,
	errorLogChan chan<- messages.LogErrorMessage,
	dataWriteChan chan<- messages.RealtimeDataMessage,
	shutdownChannel <-chan struct{},
	commandChannel <-chan interface{},
) (*McReader, error) {
	log.Printf("[Mcp1ExReader-%s] NewMcReader: 正在为 %s:%d 初始化", cfg.LineCode, cfg.PlcIPAddress, cfg.PlcPort)

	if errorLogChan == nil || dataWriteChan == nil || shutdownChannel == nil || commandChannel == nil {
		return nil, fmt.Errorf("NewMcReader (产线 %s): channel 参数不能为 nil", cfg.LineCode)
	}

	// The client is created here, but connection is established on first use.
	// client, err := NewClient1E(cfg.PlcIPAddress, cfg.PlcPort, cfg.ConnectTimeout)
	// if err != nil {
	// 	return nil, fmt.Errorf("为产线 %s 创建 MC 客户端失败: %w", cfg.LineCode, err)
	// }
	// 构造 mcClient，但**不立即拨号**；连接将在轮询循环首次 Ping 时建立，
	// 行为与 ModbusReader 保持一致，避免因一次超时而放弃整个 Reader。
	client := &mcClient{
		addr:        fmt.Sprintf("%s:%d", cfg.PlcIPAddress, cfg.PlcPort),
		dialTimeout: cfg.ConnectTimeout,
		logger:      log.Default(), // 可替换为自定义 logger
	}

	// Setup the centralized logger with default context for this reader
	source := fmt.Sprintf("Mcp1ExReader-%s", cfg.LineCode)
	defaultLogDetails := map[string]interface{}{
		"line_code": cfg.LineCode,
		"plc_ip":    cfg.PlcIPAddress,
		"plc_port":  cfg.PlcPort,
	}
	logger := utils.NewLogger(errorLogChan, source, defaultLogDetails)

	// Initialize the data handler which will manage all state, passing the logger to it
	dataHandler := utils.NewCumulativeDataHandler(
		cfg.LineCode,
		initialCumulativeInput, initialAppLastKnownPLCInput,
		initialCumulativeOutput, initialAppLastKnownPLCOutput,
		initialBaselineInput, initialBaselineOutput,
		initialCompensationInput, initialCompensationOutput,
		dataWriteChan,
		logger,
	)

	mr := &McReader{
		config:          cfg,
		client:          client,
		shutdownChannel: shutdownChannel,
		commandChannel:  commandChannel,
		dataHandler:     dataHandler,
		logger:          logger,
	}

	log.Printf(
		"[Mcp1ExReader-%s] NewMcReader: 初始化成功. Data handler initialized.",
		cfg.LineCode,
	)
	return mr, nil
}

// Start begins the MC protocol polling loop.
// This logic is now aligned with ModbusReader for consistency and robustness.
func (mr *McReader) Start(wg *sync.WaitGroup) {
	log.Printf("[Mcp1ExReader-%s] 启动轮询循环...", mr.config.LineCode)

	wg.Add(1)
	go func() {
		defer wg.Done()
		defer func() {
			if mr.client != nil {
				log.Printf("[Mcp1ExReader-%s] 在 defer 函数中关闭 MC 连接...", mr.config.LineCode)
				mr.client.Close()
			}
			log.Printf("[Mcp1ExReader-%s] 轮询循环已停止。", mr.config.LineCode)
		}()

		// Initialize the new BackoffController
		backoffController := utils.NewBackoffController(utils.BackoffConfig{
			PollingInterval:            mr.config.PollingInterval,
			BackoffIntervalsSec:        mr.config.BackoffIntervalsSec,
			FailuresToNextBackoffLevel: mr.config.FailuresToNextBackoffLevel,
		}, fmt.Sprintf("Mcp1ExReader-%s", mr.config.LineCode))

		var isConnected bool
		ticker := time.NewTicker(mr.config.PollingInterval)
		defer ticker.Stop()
		backoffController.ResetTicker(ticker) // Initial ticker setup

		for {
			select {
			case <-mr.shutdownChannel:
				log.Printf("[Mcp1ExReader-%s] 收到关闭信号，正在停止轮询循环。", mr.config.LineCode)
				return

			case cmd, ok := <-mr.commandChannel:
				if !ok {
					log.Printf("[Mcp1ExReader-%s] 命令通道已关闭，可能正在关闭。", mr.config.LineCode)
					continue
				}
				log.Printf("[Mcp1ExReader-%s] 从命令通道收到消息，类型: %T", mr.config.LineCode, cmd)
				mr.dataHandler.HandleCommand(cmd)

			case <-ticker.C:
				log.Printf("[Mcp1ExReader-%s] 轮询信号。%s",
					mr.config.LineCode, backoffController.GetStatusLine())

				if backoffController.ShouldSkipCycle() {
					continue
				}

				var operationError error

				// 1. Ensure connection
				if !isConnected {
					log.Printf("[Mcp1ExReader-%s] 未连接。正在通过 Ping 建立到 %s 的连接...", mr.config.LineCode, mr.config.PlcIPAddress)
					ctx, cancel := context.WithTimeout(context.Background(), mr.config.ConnectTimeout)
					err := mr.client.Ping(ctx)
					cancel()
					if err != nil {
						log.Printf("[Mcp1ExReader-%s] 连接 Ping 失败: %v", mr.config.LineCode, err)
						isConnected = false
						operationError = err // Assign connection error to the unified error variable
						mr.logger.Send(messages.SeverityError, fmt.Sprintf("连接 Ping 失败: %v", err), nil)
					} else {
						log.Printf("[Mcp1ExReader-%s] 连接 Ping 成功。", mr.config.LineCode)
						isConnected = true
					}
				}

				// 2. Read data only if connected and no error has occurred yet.
				var plcRawInput, plcRawInputTime, plcRawOutput, plcRawOutputTime int
				if isConnected && operationError == nil {
					log.Printf("[Mcp1ExReader-%s] 正在读取数据...", mr.config.LineCode)

					var readError error

					readMcReg := func(addrStr string) (int, error) {
						if addrStr == "" {
							return 0, nil
						}
						dev, addr, err := parseAddress(addrStr)
						if err != nil {
							return 0, fmt.Errorf("地址格式无效 '%s': %w", addrStr, err)
						}
						if dev == "" { // Should not happen if addrStr is not empty, but as a safeguard
							return 0, nil
						}

						ctx, cancel := context.WithTimeout(context.Background(), mr.config.ResponseTimeout)
						defer cancel()

						val, err := mr.client.ReadWords(ctx, dev, addr, 1)
						if err != nil {
							return 0, fmt.Errorf("从 %s (设备:%s 地址:%d) 读取失败: %w", addrStr, dev, addr, err)
						}
						if len(val) < 1 {
							return 0, fmt.Errorf("从地址 %s 读取未返回任何数据", addrStr)
						}
						return int(val[0]), nil
					}

					plcRawInput, readError = readMcReg(mr.config.InputAddress)
					if readError == nil {
						plcRawInputTime, readError = readMcReg(mr.config.InputTimeAddress)
					}
					if readError == nil {
						plcRawOutput, readError = readMcReg(mr.config.OutputAddress)
					}
					if readError == nil {
						plcRawOutputTime, readError = readMcReg(mr.config.OutputTimeAddress)
					}

					if readError != nil {
						log.Printf("[Mcp1ExReader-%s] 处理 MC 数据时出错: %v", mr.config.LineCode, readError)
						isConnected = false // Mark as disconnected to force a reconnect attempt next cycle
						mr.client.Close()
						operationError = readError // Assign read error to the unified error variable
						mr.logger.Send(messages.SeverityError, fmt.Sprintf("处理 MC 数据时出错: %v", readError), nil)
					}
				}

				// 3. Process data or handle failure
				if operationError == nil { // --- SUCCESS PATH ---
					mr.dataHandler.ProcessReadData(
						plcRawInput, plcRawOutput,
						plcRawInputTime, plcRawOutputTime,
						mr.config.InputAddress, mr.config.OutputAddress,
					)

					// On full success, notify the backoff controller
					backoffController.HandleSuccess()
					backoffController.ResetTicker(ticker)
				} else {
					// --- FAILURE PATH (unified backoff logic) ---
					backoffController.HandleFailure()
					backoffController.ResetTicker(ticker)
				}
			} // end select
		} // end for
	}() // end go func
}
